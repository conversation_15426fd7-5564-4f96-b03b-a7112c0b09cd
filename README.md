# AirMonitor 空调监控调试软件

## 项目概括
本项目旨在开发一个基于 WPF 和 .NET 8.0 的商用空调监控调试桌面应用程序，采用MVVM架构模式，提供直观的用户界面和流畅的用户体验。项目包含主应用程序、共享类库和License注册机工具，通过精细化的License授权机制实现软件功能的精确控制。

## 技术选型
- **开发框架**: WPF (Windows Presentation Foundation)
- **.NET 版本**: .NET 8.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **UI实现策略**: **仅使用原生WPF控件** (Button、TextBox、DataGrid、ListView等)
- **界面设计**: 使用WPF控件的默认样式，进行必要的基础调整
- **数据访问**: 文件系统操作、配置管理
- **加密技术**: License文件加密/解密
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **消息传递**: CommunityToolkit.Mvvm
- **版本控制**: Git
- **其他工具**: NUnit (测试), Serilog (日志)

## WPF 项目结构 / 模块划分

### 主项目 (AirMonitor)
- `/Views/`: XAML 视图文件
  - `MainWindow.xaml`: 主窗口
  - `LicenseView.xaml`: License验证界面
  - `[业务模块]View.xaml`: 业务功能模块视图（待后续添加）
- `/ViewModels/`: 视图模型类
  - `MainViewModel.cs`: 主窗口视图模型
  - `LicenseViewModel.cs`: License验证视图模型
  - `[业务模块]ViewModel.cs`: 业务功能模块视图模型（待后续添加）
- `/Models/`: 数据模型类
  - `LicenseInfo.cs`: License信息模型
- `/Services/`: 业务服务层
  - `LicenseService.cs`: License验证服务
- `/Converters/`: 值转换器
- `/Controls/`: 自定义用户控件
- `/Resources/`: 资源文件
  - `/Styles/`: 样式文件
  - `/Images/`: 图片资源
- `/Utils/`: 工具类和辅助函数
- `App.xaml`: 应用程序定义
- `MainWindow.xaml`: 主窗口

### 共享项目 (AirMonitor.Shared)
- `/License/`: License相关类
  - `LicenseManager.cs`: License管理器
  - `LicenseValidator.cs`: License验证器
  - `EncryptionHelper.cs`: 加密解密工具
- `/Models/`: 共享数据模型
  - `LicenseModel.cs`: License数据模型
  - `DeviceInfo.cs`: 设备信息模型
- `/Utils/`: 共享工具类
  - `DeviceFingerprint.cs`: 设备指纹生成
  - `FileHelper.cs`: 文件操作工具
- `/Constants/`: 常量定义
  - `LicenseConstants.cs`: License相关常量

### 注册机项目 (AirMonitor.LicenseGenerator)
- `/Views/`: XAML 视图文件
  - `MainWindow.xaml`: 主窗口
  - `LicenseGeneratorView.xaml`: License生成界面
- `/ViewModels/`: 视图模型类
  - `MainViewModel.cs`: 主窗口视图模型
  - `LicenseGeneratorViewModel.cs`: License生成视图模型
- `/Models/`: 数据模型类
  - `LicenseConfig.cs`: License配置模型
- `/Services/`: 业务服务层
  - `LicenseGeneratorService.cs`: License生成服务
- `App.xaml`: 应用程序定义
- `MainWindow.xaml`: 主窗口

## 核心功能模块 / 界面详解

### License授权验证模块
- `License验证界面`: 应用程序启动时的License文件验证，支持License文件选择和验证状态显示
- `授权管理模块`: 基于License内容进行界面和按钮级别的权限控制

### License注册机模块
- `设备指纹生成`: 自动获取设备硬件信息生成唯一设备指纹
- `授权配置界面`: 通过勾选方式配置具体的功能权限（界面级、按钮级）
- `有效期设置`: 支持设置License的有效期限
- `License文件生成`: 生成加密的License文件

### 业务功能模块
- **注意**: 具体的业务功能模块将在后续开发过程中根据需求逐步添加，当前不预设任何业务功能

## License授权机制设计

### 授权颗粒度示例
- **界面级控制**: 控制特定界面的访问权限
- **按钮级控制**: 控制界面内特定按钮的可用性
- **功能模块控制**: 控制整个功能模块的启用/禁用

### License文件结构
- 设备指纹信息
- 授权功能列表
- 有效期信息
- 数字签名/校验码

### 验证流程
1. 应用程序启动时读取License文件
2. 验证设备指纹匹配性
3. 检查License有效期
4. 验证文件完整性和签名
5. 根据授权信息控制界面元素的可用性

## WPF 架构设计
- **View层**: 负责用户界面展示，使用XAML定义界面布局和样式
- **ViewModel层**: 作为View和Model之间的桥梁，处理界面逻辑和数据绑定
- **Model层**: 定义业务数据模型和业务逻辑
- **Service层**: 提供数据访问、业务服务等功能
- **依赖注入**: 使用DI容器管理对象生命周期和依赖关系

## 界面设计规范 (基于原生WPF控件)
- **设计理念**: 使用WPF控件的默认样式，保持标准Windows应用程序外观
- **色彩方案**: 使用系统默认颜色（SystemColors），定义少量应用程序级别颜色
- **字体规范**: 使用系统默认字体（SystemFonts），统一应用程序字体设置
- **原生控件使用**: 保持Button、TextBox、DataGrid等控件的默认外观
- **布局原则**: 使用Grid、StackPanel等原生布局控件，遵循标准布局规范
- **简化设计**: 避免复杂的样式定制和动画效果，专注功能实现

## 技术实现细节

### License验证界面模块 {#license-verification-module}

#### MVVM架构实现
- **Model层**: `LicenseInfo.cs` - 定义License信息数据模型，包含文件路径、验证状态、有效期、授权功能等属性
- **ViewModel层**: `LicenseViewModel.cs` - 使用CommunityToolkit.Mvvm实现属性绑定和命令绑定
- **View层**: `LicenseView.xaml` - 原生WPF控件构建的License验证界面
- **Service层**: `LicenseService.cs` - License验证业务逻辑服务

#### 数据绑定策略
- 使用`ObservableProperty`特性实现属性变更通知
- 使用`RelayCommand`特性实现命令绑定
- 通过DataTrigger实现界面状态的动态控制
- 双向数据绑定实现界面与ViewModel的实时同步

#### 关键技术特性
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection管理服务生命周期
- **异步验证**: License验证过程采用异步模式，避免界面阻塞
- **状态管理**: 通过ViewModel属性控制界面元素的可见性和可用性
- **文件选择**: 集成OpenFileDialog实现License文件选择功能

#### XAML界面设计
- 使用Grid布局实现响应式界面结构
- 采用GroupBox对功能区域进行逻辑分组
- 使用原生WPF控件（Button、TextBox、ProgressBar等）保持标准外观
- 通过Style和DataTrigger实现动态界面效果

#### 验证流程实现
1. 应用程序启动时显示License验证窗口
2. 用户选择License文件或使用默认路径
3. 点击验证按钮触发异步验证流程
4. 显示验证进度和结果信息
5. 验证成功后允许用户继续使用软件

### 共享License验证逻辑模块 {#shared-license-logic-module}

#### 核心组件架构
- **LicenseManager**: 统一的License管理器，提供加载、验证、权限检查等核心功能
- **LicenseValidator**: 专门的License验证器，实现完整的验证流程
- **EncryptionHelper**: 加密解密工具类，支持AES加密和SHA256签名
- **DeviceFingerprint**: 设备指纹生成工具，获取硬件信息生成唯一标识
- **LicenseModel**: 完整的License数据模型，支持JSON序列化
- **DeviceInfo**: 设备信息模型，封装硬件信息获取逻辑

#### License验证流程
1. **文件存在性检查**: 验证License文件是否存在
2. **文件解密**: 使用AES算法解密License文件内容
3. **数据反序列化**: 将JSON内容转换为LicenseModel对象
4. **签名验证**: 使用SHA256验证License数据完整性
5. **设备指纹验证**: 对比当前设备指纹与License中的指纹
6. **有效期验证**: 检查License是否在有效期内
7. **权限控制**: 基于验证结果控制功能、界面、按钮级别的访问权限

#### 设备指纹技术
- **硬件信息采集**: 主板序列号、CPU序列号、硬盘序列号、MAC地址
- **系统信息**: 计算机名称、操作系统版本
- **指纹生成**: 使用SHA256算法生成唯一设备标识
- **并行获取**: 多线程并行获取硬件信息，提高性能
- **容错机制**: 硬件信息获取失败时使用备用方案

#### 加密安全机制
- **AES加密**: 使用AES-CBC模式加密License文件内容
- **SHA256签名**: 对关键License数据生成数字签名防篡改
- **密钥管理**: 内置加密密钥和初始化向量
- **数据完整性**: 多层验证确保License数据未被修改

#### 权限控制颗粒度
- **功能级权限**: 控制具体业务功能的启用/禁用
- **界面级权限**: 控制特定界面的访问权限
- **按钮级权限**: 精确控制界面内按钮的可用性
- **动态权限检查**: 运行时实时检查权限状态

## 开发状态跟踪
| 功能模块/界面           | View状态 | ViewModel状态 | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------|----------|---------------|--------|--------------|--------------|-----------|
| License验证界面         | 已完成   | 已完成        | AI     | 2025-01-07   | 2025-01-07   | [技术实现](#license-verification-module) |
| License注册机界面       | 未开始   | 未开始        | AI     | 待定         |              |           |
| 共享License验证逻辑     | 已完成   | 已完成        | AI     | 2025-01-07   | 2025-01-07   | [技术实现](#shared-license-logic-module) |
| 设备指纹生成功能        | 已完成   | 已完成        | AI     | 2025-01-07   | 2025-01-07   | [包含在共享逻辑中](#shared-license-logic-module) |
| 主窗口基础架构          | 未开始   | 未开始        | AI     | 待定         |              |           |

## 代码检查与问题记录
[本部分用于记录WPF代码检查结果和开发过程中遇到的问题及其解决方案，包括XAML验证、数据绑定问题、性能优化等。]

## 环境设置与运行指南
- **开发环境**: Visual Studio 2022 或更高版本
- **.NET版本**: .NET 8.0 SDK
- **运行命令**: `dotnet run --project AirMonitor`
- **测试命令**: `dotnet test`
- **生成命令**: `dotnet build`

## WPF性能优化
[记录WPF应用程序的性能优化策略和实施情况。]

## 部署指南
[包含WPF应用程序的打包、发布和部署相关信息。]
