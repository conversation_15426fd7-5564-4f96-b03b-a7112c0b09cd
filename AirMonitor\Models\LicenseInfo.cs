using System;

namespace AirMonitor.Models
{
    /// <summary>
    /// License信息模型
    /// </summary>
    public class LicenseInfo
    {
        /// <summary>
        /// License文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 是否已验证
        /// </summary>
        public bool IsValidated { get; set; }

        /// <summary>
        /// 验证状态消息
        /// </summary>
        public string StatusMessage { get; set; } = string.Empty;

        /// <summary>
        /// License有效期
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 授权的功能列表
        /// </summary>
        public List<string> AuthorizedFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 设备指纹
        /// </summary>
        public string DeviceFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// License是否有效
        /// </summary>
        public bool IsValid => IsValidated && 
                              (ExpiryDate == null || ExpiryDate > DateTime.Now);
    }
}
