# AirMonitor License系统使用指南

## 概述
AirMonitor采用基于设备指纹的License授权机制，确保软件在授权设备上正常运行。本指南详细说明了License系统的使用流程。

## License类型说明

| License类型 | 适用场景 | 权限范围 |
|------------|----------|----------|
| 普通 | 一般用户使用 | 基础监控、数据导出、系统设置 |
| 售后 | 售后服务人员 | 普通权限 + 高级监控、设备控制 |
| 研发 | 研发测试人员 | 售后权限 + 报表生成、报警管理 |
| 管理 | 管理员使用 | 全部权限 + 用户管理 |

## 完整使用流程

### 步骤1：获取设备指纹

#### 方法一：在主程序中获取（推荐）
1. 运行AirMonitor主程序：`dotnet run --project AirMonitor`
2. 如果License验证失败，会显示License验证界面
3. 在界面中点击"获取当前设备指纹"按钮
4. 系统会显示设备详细信息，包括设备指纹
5. 点击"复制指纹"按钮将设备指纹复制到剪贴板
6. 将设备指纹提供给管理员

#### 方法二：使用独立工具获取
1. 运行设备指纹生成工具：
   ```bash
   # 需要先编译共享库
   dotnet build AirMonitor.Shared
   # 然后运行生成工具
   dotnet run DeviceFingerprintGenerator.cs
   ```
2. 工具会显示完整的设备信息和指纹
3. 复制设备指纹字符串

### 步骤2：生成License文件

1. 运行License生成器：`dotnet run --project AirMonitor.LicenseGenerator`

2. 在"基本信息"标签页中：
   - 选择License类型（普通/售后/研发/管理）
   - 在设备指纹输入框中粘贴步骤1获取的设备指纹
   - 设置有效期（永久有效或指定天数）

3. 在"功能权限"标签页中：
   - 根据需要勾选授权的功能
   - 可使用"全选"或"取消全选"快速操作

4. 在"界面权限"标签页中：
   - 选择用户可以访问的界面

5. 在"按钮权限"标签页中：
   - 选择用户可以使用的具体按钮功能

6. 设置输出文件路径，点击"生成License"

### 步骤3：使用License文件

1. 将生成的License文件（.lic）放置到AirMonitor程序目录下，命名为`license.lic`

2. 运行AirMonitor主程序：`dotnet run --project AirMonitor`

3. 程序会自动检查License文件：
   - 如果License有效，直接进入主程序
   - 如果License无效或不存在，显示License验证界面

4. 在License验证界面中：
   - 可以选择其他License文件
   - 可以获取当前设备指纹（用于重新生成License）

## 常见问题解决

### Q1: License验证失败怎么办？
**A1:** 
1. 检查License文件是否存在且路径正确
2. 在License验证界面获取当前设备指纹
3. 确认License是否是为当前设备生成的
4. 检查License是否已过期
5. 联系管理员重新生成License

### Q2: 设备指纹获取失败怎么办？
**A2:**
1. 确保程序有足够的系统权限
2. 检查防病毒软件是否阻止了硬件信息访问
3. 尝试以管理员身份运行程序
4. 如果仍然失败，可以手动提供设备信息给管理员

### Q3: License文件损坏怎么办？
**A3:**
1. 重新获取设备指纹
2. 联系管理员重新生成License文件
3. 确保License文件传输过程中没有被修改

### Q4: 更换硬件后License失效怎么办？
**A4:**
1. 硬件变更会导致设备指纹改变
2. 需要重新获取新的设备指纹
3. 联系管理员生成新的License文件

## 技术说明

### 设备指纹组成
设备指纹由以下硬件信息生成：
- 主板序列号
- CPU序列号  
- 硬盘序列号
- MAC地址
- 计算机名称

### License文件格式
- 文件扩展名：.lic
- 内容格式：加密的JSON数据
- 包含信息：设备指纹、有效期、授权权限、数字签名

### 安全机制
- AES加密保护License文件内容
- SHA256数字签名防止文件篡改
- 设备指纹绑定确保License不被盗用
- 有效期控制确保授权时效性

## 联系支持
如果在使用过程中遇到问题，请联系技术支持并提供：
1. 错误信息截图
2. 设备指纹信息
3. License文件（如果有）
4. 操作系统版本信息
