using AirMonitor.LicenseGenerator.Models;
using AirMonitor.LicenseGenerator.Services;
using AirMonitor.Shared.Constants;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace AirMonitor.LicenseGenerator.ViewModels
{
    /// <summary>
    /// License生成器ViewModel
    /// </summary>
    public partial class LicenseGeneratorViewModel : ObservableObject
    {
        private readonly LicenseGeneratorService _generatorService;

        [ObservableProperty]
        private string _customerName = string.Empty;

        [ObservableProperty]
        private string _customerEmail = string.Empty;

        [ObservableProperty]
        private string _selectedLicenseType = LicenseConstants.LicenseTypes.STANDARD;

        [ObservableProperty]
        private string _deviceFingerprint = string.Empty;

        [ObservableProperty]
        private bool _isPermanent = true;

        [ObservableProperty]
        private int _validDays = 365;

        [ObservableProperty]
        private int _maxConcurrentUsers = 1;

        [ObservableProperty]
        private string _outputFilePath = string.Empty;

        [ObservableProperty]
        private bool _isGenerating = false;

        [ObservableProperty]
        private string _statusMessage = "准备生成License";

        [ObservableProperty]
        private bool _isDeviceInfoLoaded = false;

        [ObservableProperty]
        private string _deviceInfoText = string.Empty;

        public ObservableCollection<FeatureItem> AvailableFeatures { get; } = new();
        public ObservableCollection<ViewItem> AvailableViews { get; } = new();
        public ObservableCollection<ButtonItem> AvailableButtons { get; } = new();

        public string[] LicenseTypes { get; } = new[]
        {
            LicenseConstants.LicenseTypes.TRIAL,
            LicenseConstants.LicenseTypes.STANDARD,
            LicenseConstants.LicenseTypes.PROFESSIONAL,
            LicenseConstants.LicenseTypes.ENTERPRISE
        };

        public LicenseGeneratorViewModel(LicenseGeneratorService generatorService)
        {
            _generatorService = generatorService;
            
            // 初始化默认输出路径
            var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            OutputFilePath = Path.Combine(desktopPath, "license.lic");

            // 加载权限列表
            LoadPermissionLists();
        }

        /// <summary>
        /// 获取当前设备指纹命令
        /// </summary>
        [RelayCommand]
        private async Task GetDeviceFingerprintAsync()
        {
            try
            {
                StatusMessage = "正在获取设备指纹...";
                
                var deviceInfo = await _generatorService.GetCurrentDeviceInfoAsync();
                DeviceFingerprint = deviceInfo.GenerateFingerprint();
                
                // 显示设备信息
                DeviceInfoText = $"计算机名: {deviceInfo.ComputerName}\n" +
                               $"操作系统: {deviceInfo.OSVersion}\n" +
                               $"主板序列号: {deviceInfo.MotherboardSerial}\n" +
                               $"CPU序列号: {deviceInfo.CpuSerial}\n" +
                               $"硬盘序列号: {deviceInfo.HardDiskSerial}\n" +
                               $"MAC地址: {deviceInfo.MacAddress}\n" +
                               $"设备指纹: {DeviceFingerprint}";
                
                IsDeviceInfoLoaded = true;
                StatusMessage = "设备指纹获取成功";
            }
            catch (Exception ex)
            {
                StatusMessage = $"获取设备指纹失败: {ex.Message}";
                MessageBox.Show($"获取设备指纹失败：{ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 选择输出文件路径命令
        /// </summary>
        [RelayCommand]
        private void SelectOutputPath()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "选择License文件保存位置",
                Filter = "License文件 (*.lic)|*.lic|所有文件 (*.*)|*.*",
                DefaultExt = "lic",
                FileName = "license.lic"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                OutputFilePath = saveFileDialog.FileName;
            }
        }

        /// <summary>
        /// 生成License命令
        /// </summary>
        [RelayCommand]
        private async Task GenerateLicenseAsync()
        {
            if (IsGenerating) return;

            try
            {
                IsGenerating = true;
                StatusMessage = "正在生成License...";

                // 创建配置
                var config = CreateLicenseConfig();

                // 生成License
                var result = await _generatorService.GenerateLicenseAsync(config);

                if (result.Success)
                {
                    StatusMessage = "License生成成功";
                    MessageBox.Show(result.Message, "成功", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    StatusMessage = "License生成失败";
                    MessageBox.Show(result.Message, "错误", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"生成License时发生错误: {ex.Message}";
                MessageBox.Show($"生成License时发生错误：{ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsGenerating = false;
            }
        }

        /// <summary>
        /// 全选功能权限命令
        /// </summary>
        [RelayCommand]
        private void SelectAllFeatures()
        {
            foreach (var feature in AvailableFeatures)
            {
                feature.IsSelected = true;
            }
        }

        /// <summary>
        /// 取消全选功能权限命令
        /// </summary>
        [RelayCommand]
        private void UnselectAllFeatures()
        {
            foreach (var feature in AvailableFeatures)
            {
                feature.IsSelected = false;
            }
        }

        /// <summary>
        /// 全选界面权限命令
        /// </summary>
        [RelayCommand]
        private void SelectAllViews()
        {
            foreach (var view in AvailableViews)
            {
                view.IsSelected = true;
            }
        }

        /// <summary>
        /// 取消全选界面权限命令
        /// </summary>
        [RelayCommand]
        private void UnselectAllViews()
        {
            foreach (var view in AvailableViews)
            {
                view.IsSelected = false;
            }
        }

        /// <summary>
        /// 全选按钮权限命令
        /// </summary>
        [RelayCommand]
        private void SelectAllButtons()
        {
            foreach (var button in AvailableButtons)
            {
                button.IsSelected = true;
            }
        }

        /// <summary>
        /// 取消全选按钮权限命令
        /// </summary>
        [RelayCommand]
        private void UnselectAllButtons()
        {
            foreach (var button in AvailableButtons)
            {
                button.IsSelected = false;
            }
        }

        /// <summary>
        /// 加载权限列表
        /// </summary>
        private void LoadPermissionLists()
        {
            // 加载功能权限
            var features = _generatorService.GetAvailableFeatures();
            AvailableFeatures.Clear();
            foreach (var feature in features)
            {
                AvailableFeatures.Add(feature);
            }

            // 加载界面权限
            var views = _generatorService.GetAvailableViews();
            AvailableViews.Clear();
            foreach (var view in views)
            {
                AvailableViews.Add(view);
            }

            // 加载按钮权限
            var buttons = _generatorService.GetAvailableButtons();
            AvailableButtons.Clear();
            foreach (var button in buttons)
            {
                AvailableButtons.Add(button);
            }
        }

        /// <summary>
        /// 创建License配置
        /// </summary>
        /// <returns>License配置</returns>
        private LicenseConfig CreateLicenseConfig()
        {
            var config = new LicenseConfig
            {
                CustomerName = CustomerName,
                CustomerEmail = CustomerEmail,
                LicenseType = SelectedLicenseType,
                DeviceFingerprint = DeviceFingerprint,
                IsPermanent = IsPermanent,
                ValidDays = ValidDays,
                MaxConcurrentUsers = MaxConcurrentUsers,
                OutputFilePath = OutputFilePath
            };

            // 添加选中的功能权限
            config.AuthorizedFeatures.AddRange(
                AvailableFeatures.Where(f => f.IsSelected).Select(f => f.Name));

            // 添加选中的界面权限
            config.AuthorizedViews.AddRange(
                AvailableViews.Where(v => v.IsSelected).Select(v => v.Name));

            // 添加选中的按钮权限
            config.AuthorizedButtons.AddRange(
                AvailableButtons.Where(b => b.IsSelected).Select(b => b.Name));

            return config;
        }
    }
}
