using System;
using System.IO;
using System.Threading.Tasks;

namespace AirMonitor.Shared.Utils
{
    /// <summary>
    /// 文件操作工具类
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// 确保目录存在，如果不存在则创建
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        public static void EnsureDirectoryExists(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }

        /// <summary>
        /// 安全地读取文件内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容，失败时返回空字符串</returns>
        public static async Task<string> SafeReadAllTextAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return string.Empty;

                return await File.ReadAllTextAsync(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件失败 {filePath}: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 安全地写入文件内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">文件内容</param>
        /// <returns>是否成功</returns>
        public static async Task<bool> SafeWriteAllTextAsync(string filePath, string content)
        {
            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory))
                {
                    EnsureDirectoryExists(directory);
                }

                await File.WriteAllTextAsync(filePath, content);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入文件失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 安全地复制文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        /// <returns>是否成功</returns>
        public static bool SafeCopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                    return false;

                // 确保目标目录存在
                var directory = Path.GetDirectoryName(destinationFilePath);
                if (!string.IsNullOrEmpty(directory))
                {
                    EnsureDirectoryExists(directory);
                }

                File.Copy(sourceFilePath, destinationFilePath, overwrite);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"复制文件失败 {sourceFilePath} -> {destinationFilePath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 安全地删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public static bool SafeDeleteFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除文件失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取文件大小（字节）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件大小，失败时返回-1</returns>
        public static long GetFileSize(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return -1;

                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件大小失败 {filePath}: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 获取文件的最后修改时间
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>最后修改时间，失败时返回null</returns>
        public static DateTime? GetLastWriteTime(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return null;

                return File.GetLastWriteTime(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件修改时间失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查文件是否被占用
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否被占用</returns>
        public static bool IsFileInUse(string filePath)
        {
            if (!File.Exists(filePath))
                return false;

            try
            {
                using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.None);
                return false;
            }
            catch (IOException)
            {
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查文件占用状态失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成备份文件名
        /// </summary>
        /// <param name="originalFilePath">原始文件路径</param>
        /// <returns>备份文件路径</returns>
        public static string GenerateBackupFileName(string originalFilePath)
        {
            var directory = Path.GetDirectoryName(originalFilePath) ?? string.Empty;
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(originalFilePath);
            var extension = Path.GetExtension(originalFilePath);
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            return Path.Combine(directory, $"{fileNameWithoutExtension}_backup_{timestamp}{extension}");
        }
    }
}
