{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AirMonitor/1.0.0": {"dependencies": {"AirMonitor.Shared": "1.0.0", "CommunityToolkit.Mvvm": "8.4.0", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Hosting": "9.0.5"}, "runtime": {"AirMonitor.dll": {}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.Configuration/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Json/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Json": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.5": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileSystemGlobbing": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Hosting/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.Configuration.CommandLine": "9.0.5", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.5", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.5", "Microsoft.Extensions.Configuration.Json": "9.0.5", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.5", "Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Physical": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.Logging.Console": "9.0.5", "Microsoft.Extensions.Logging.Debug": "9.0.5", "Microsoft.Extensions.Logging.EventLog": "9.0.5", "Microsoft.Extensions.Logging.EventSource": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Console/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Debug/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "System.Diagnostics.EventLog": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System.Security.Cryptography.Apple/4.3.1": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.1"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.1": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "System.CodeDom/9.0.5": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Diagnostics.EventLog/9.0.5": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/9.0.5": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Management/9.0.5": {"dependencies": {"System.CodeDom": "9.0.5"}, "runtime": {"lib/net8.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.1", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/9.0.5": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Text.Json/9.0.5": {"dependencies": {"System.IO.Pipelines": "9.0.5", "System.Text.Encodings.Web": "9.0.5"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "AirMonitor.Shared/1.0.0": {"dependencies": {"System.Management": "9.0.5", "System.Security.Cryptography.Algorithms": "4.3.1", "System.Text.Json": "9.0.5"}, "runtime": {"AirMonitor.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AirMonitor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-uYXLg2Gt8KUH5nT3u+TBpg9VrRcN5+2zPmIjqEHR4kOoBwsbtMDncEJw9HiLvZqGgIo2TR4oraibAoy5hXn2bQ==", "path": "microsoft.extensions.configuration/9.0.5", "hashPath": "microsoft.extensions.configuration.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-7pQ4Tkyofm8DFWFhqn9ZmG8qSAC2VitWleATj5qob9V9KtoxCVdwRtmiVl/ha3WAgjkEfW++JLWXox9MJwMgkg==", "path": "microsoft.extensions.configuration.binder/9.0.5", "hashPath": "microsoft.extensions.configuration.binder.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-BloAPG22eV+F4CpGKg0lHeXsLxbsGeId4mNpNsUc250j79pcJL3OWVRgmyIUBP5eF74lYJlaOVF+54MRBAQV3A==", "path": "microsoft.extensions.configuration.commandline/9.0.5", "hashPath": "microsoft.extensions.configuration.commandline.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-kfLv3nbn3tt42g/YfPMJGW6SJRt4DLIvSu5njrZv622kBGVOXBMwyoqFLvR/tULzn0mwICJu6GORdUJ+INpexg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.5", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ifrA7POOJ7EeoEJhC8r03WufBsEV4zgnTLQURHh1QIS/vU6ff/60z8M4tD3i2csdFPREEc1nGbiOZhi7Q5aMfw==", "path": "microsoft.extensions.configuration.fileextensions/9.0.5", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-LiWV+Sn5yvoQEd/vihGwkR3CZ4ekMrqP5OQiYOlbzMBfBa6JHBWBsTO5ta6dMYO9ADMiv9K6GBKJSF9DrP29sw==", "path": "microsoft.extensions.configuration.json/9.0.5", "hashPath": "microsoft.extensions.configuration.json.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-DONkv4TzvCUps55pu+667HasjhW5WoKndDPt9AvnF3qnYfgh+OXN01cDdH0h9cfXUXluzAZfGhqh/Uwt14aikg==", "path": "microsoft.extensions.configuration.usersecrets/9.0.5", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-fRiUjmhm9e4vMp6WEO9MgWNxVtWSr4Pcgh1W4DyJIr8bRANlZz9JU7uicf7ShzMspDxo/9Ejo9zJ6qQZY0IhVw==", "path": "microsoft.extensions.diagnostics/9.0.5", "hashPath": "microsoft.extensions.diagnostics.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6YfTcULCYREMTqtk+s3UiszsFV2xN2FXtxdQpurmQJY9Cp/QGiM4MTKfJKUo7AzdLuzjOKKMWjQITmvtK7AsUg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.5", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-LLm+e8lvD+jOI+blHRSxPqywPaohOTNcVzQv548R1UpkEiNB2D+zf3RrqxBdB1LDPicRMTnfiaKJovxF8oX1bQ==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.5", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cMQqvK0rclKzAm2crSFe9JiimR+wzt6eaoRxa8/mYFkqekY4JEP8eShVZs4NPsKV2HQFHfDgwfFSsWUrUgqbKA==", "path": "microsoft.extensions.fileproviders.physical/9.0.5", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-TWJZJGIyUncH4Ah+Sy9X5mPJeoz02lRlFx9VWaFo4b4o0tkA1dk2u6HRHrfEC2L6N4IC+vFzfRWol1egyQqLtg==", "path": "microsoft.extensions.filesystemglobbing/9.0.5", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-PoTG6ptucJyxrrALQgRE5lwUMaSc3PK5vtEXuazEJ6mDQ9xRFmxElZCe81duH/TNH7+X/CVDVIZu6Ji2OQW4zQ==", "path": "microsoft.extensions.hosting/9.0.5", "hashPath": "microsoft.extensions.hosting.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-3GA/dxqkP6yFe18qYRgtKYuN2onC8NfhlpNN21jptkVKk7olqBTkdT49oL0pSEz2SptRsux7LocCU7+alGnEag==", "path": "microsoft.extensions.hosting.abstractions/9.0.5", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WgYTJ1/dxdzqaYYMrgC6cZXJVmaoxUmWgsvR9Kg5ZARpy0LMw7fZIZMIiVuaxhItwwFIW0ruhAN+Er2/oVZgmQ==", "path": "microsoft.extensions.logging.configuration/9.0.5", "hashPath": "microsoft.extensions.logging.configuration.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0BqgvX5y34GOrsJeAypny53OoBnXjyjQCpanrpm7dZawKv5KFk7Tqbu7LFVsRu2T0tLpQ2YHMciMiAWtp+o/Bw==", "path": "microsoft.extensions.logging.console/9.0.5", "hashPath": "microsoft.extensions.logging.console.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-IyosWdl/NM2LP72zlavSpkZyd1SczzJ+8J4LImlKWF8w/JEbqJuSJey79Wd1lJGsDj7Cik8y4CD1T2mXMIhEVA==", "path": "microsoft.extensions.logging.debug/9.0.5", "hashPath": "microsoft.extensions.logging.debug.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-KF+lvi5ZwNd5Oy5V6l0580cQjTi59boF6X4wp+2ozvUGTC4zBBsaDSVicR86pTWsDivmo9UeSlB+QgheGzrpJQ==", "path": "microsoft.extensions.logging.eventlog/9.0.5", "hashPath": "microsoft.extensions.logging.eventlog.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-H4PVv6aDt4jNyZi7MN746GtHPNRjGdH7OrueDViQDBAw/b4incGYEPbUKUACa9HED0vfI4PPaQrzz1Hz5Odh3g==", "path": "microsoft.extensions.logging.eventsource/9.0.5", "hashPath": "microsoft.extensions.logging.eventsource.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-CJbAVdovKPFh2FoKxesu20odRVSbL/vtvzzObnG+5u38sOfzRS2Ncy25id0TjYUGQzMhNnJUHgTUzTMDl/3c9g==", "path": "microsoft.extensions.options.configurationextensions/9.0.5", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-UPrVPlqPRSVZaB4ADmbsQ77KXn9ORiWXyA1RP2W2+byCh3bhgT1bQz0jbeOoog9/2oTQ5wWZSDSMeb74MjezcA==", "path": "runtime.native.system.security.cryptography.apple/4.3.1", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.1.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-t15yGf5r6vMV1rB5O6TgfXKChtCaN3niwFw44M2ImX3eZ8yzueplqMqXPCbWzoBDHJVz9fE+9LFUGCsUmS2Jgg==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.1", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.1.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "System.CodeDom/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cuzLM2MWutf9ZBEMPYYfd0DXwYdvntp7VCT6a/wvbKCa2ZuvGmW74xi+YBa2mrfEieAXqM4TNKlMmSnfAfpUoQ==", "path": "system.codedom/9.0.5", "hashPath": "system.codedom.9.0.5.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "path": "system.diagnostics.diagnosticsource/9.0.5", "hashPath": "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-nhtTvAgKTD7f6t0bkOb4/hNv0PShb8GHs5Fhn7PvYhwhyWiVyVBvL2vTGH0Hlw5jOZQmWkzQxjY6M/h4tl8M6Q==", "path": "system.diagnostics.eventlog/9.0.5", "hashPath": "system.diagnostics.eventlog.9.0.5.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-5WXo+3MGcnYn54+1ojf+kRzKq1Q6sDUnovujNJ2ky1nl1/kP3+PMil9LPbFvZ2mkhvAGmQcY07G2sfHat/v0Fw==", "path": "system.io.pipelines/9.0.5", "hashPath": "system.io.pipelines.9.0.5.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Management/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-n6o9PZm9p25+zAzC3/48K0oHnaPKTInRrxqFq1fi/5TPbMLjuoCm/h//mS3cUmSy+9AO1Z+qsC/Ilt/ZFatv5Q==", "path": "system.management/9.0.5", "hashPath": "system.management.9.0.5.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-DVUblnRfnarrI5olEC2B/OCsJQd0anjVaObQMndHSc43efbc88/RMOlDyg/EyY0ix5ecyZMXS8zMksb5ukebZA==", "path": "system.security.cryptography.algorithms/4.3.1", "hashPath": "system.security.cryptography.algorithms.4.3.1.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-HJPmqP2FsE+WVUUlTsZ4IFRSyzw40yz0ubiTnsaqm+Xo5fFZhVRvx6Zn8tLXj92/6pbre6OA4QL2A2vnCSKxJA==", "path": "system.text.encodings.web/9.0.5", "hashPath": "system.text.encodings.web.9.0.5.nupkg.sha512"}, "System.Text.Json/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "path": "system.text.json/9.0.5", "hashPath": "system.text.json.9.0.5.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "AirMonitor.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}