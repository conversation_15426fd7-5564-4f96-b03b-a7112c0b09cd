using System;

namespace AirMonitor.Shared.Models
{
    /// <summary>
    /// 设备信息模型
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 主板序列号
        /// </summary>
        public string MotherboardSerial { get; set; } = string.Empty;

        /// <summary>
        /// CPU序列号
        /// </summary>
        public string CpuSerial { get; set; } = string.Empty;

        /// <summary>
        /// 硬盘序列号
        /// </summary>
        public string HardDiskSerial { get; set; } = string.Empty;

        /// <summary>
        /// MAC地址
        /// </summary>
        public string MacAddress { get; set; } = string.Empty;

        /// <summary>
        /// 计算机名称
        /// </summary>
        public string ComputerName { get; set; } = string.Empty;

        /// <summary>
        /// 操作系统版本
        /// </summary>
        public string OSVersion { get; set; } = string.Empty;

        /// <summary>
        /// 设备指纹生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 生成设备指纹
        /// </summary>
        /// <returns>设备指纹字符串</returns>
        public string GenerateFingerprint()
        {
            var components = new[]
            {
                MotherboardSerial,
                CpuSerial,
                HardDiskSerial,
                MacAddress,
                ComputerName
            };

            var combined = string.Join(Constants.LicenseConstants.DEVICE_FINGERPRINT_SEPARATOR, components);
            
            // 使用SHA256生成指纹哈希
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combined));
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// 验证设备指纹是否匹配
        /// </summary>
        /// <param name="fingerprint">要验证的指纹</param>
        /// <returns>是否匹配</returns>
        public bool ValidateFingerprint(string fingerprint)
        {
            return GenerateFingerprint().Equals(fingerprint, StringComparison.OrdinalIgnoreCase);
        }
    }
}
