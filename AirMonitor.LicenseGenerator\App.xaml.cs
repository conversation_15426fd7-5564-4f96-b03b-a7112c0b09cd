﻿using AirMonitor.LicenseGenerator.Services;
using AirMonitor.LicenseGenerator.ViewModels;
using AirMonitor.LicenseGenerator.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Windows;

namespace AirMonitor.LicenseGenerator
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置依赖注入
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // 注册服务
                    services.AddSingleton<LicenseGeneratorService>();

                    // 注册ViewModels
                    services.AddTransient<LicenseGeneratorViewModel>();

                    // 注册Views
                    services.AddTransient<LicenseGeneratorView>();
                })
                .Build();

            // 启动License生成器窗口
            var viewModel = _host.Services.GetRequiredService<LicenseGeneratorViewModel>();
            var mainWindow = new LicenseGeneratorView(viewModel);
            mainWindow.Show();

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }
    }

}
