<Window x:Class="AirMonitor.Views.LicenseView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:AirMonitor.ViewModels"
        mc:Ignorable="d"
        Title="License验证 - AirMonitor"
        Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    


    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="AirMonitor License验证"
                   FontSize="18"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"/>

        <!-- License文件选择 -->
        <GroupBox Grid.Row="2" Header="License文件">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="10"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Row="0" Grid.Column="0"
                         Text="{Binding LicenseFilePath}"
                         IsReadOnly="True"/>

                <Button Grid.Row="0" Grid.Column="2"
                        Content="浏览..."
                        Width="80"
                        Command="{Binding SelectLicenseFileCommand}"/>

                <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"
                        Content="验证License"
                        Height="35"
                        Command="{Binding ValidateLicenseCommand}">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="IsEnabled" Value="True"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsValidating}" Value="True">
                                    <Setter Property="IsEnabled" Value="False"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </GroupBox>

        <!-- 验证状态 -->
        <GroupBox Grid.Row="4" Header="验证状态">
            <StackPanel Margin="10">
                <TextBlock Text="{Binding StatusMessage}"
                           TextWrapping="Wrap"
                           Margin="0,0,0,10"/>

                <ProgressBar Height="20"
                             IsIndeterminate="{Binding IsValidating}">
                    <ProgressBar.Style>
                        <Style TargetType="ProgressBar">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsValidating}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ProgressBar.Style>
                </ProgressBar>
            </StackPanel>
        </GroupBox>

        <!-- License详细信息 -->
        <GroupBox Grid.Row="6" Header="License信息">
            <GroupBox.Style>
                <Style TargetType="GroupBox">
                    <Setter Property="Visibility" Value="Collapsed"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsLicenseValid}" Value="True">
                            <Setter Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </GroupBox.Style>
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="10"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="10"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="有效期:" FontWeight="Bold"/>
                <TextBlock Grid.Row="0" Grid.Column="2" Text="{Binding ExpiryDateText}"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="授权功能:" FontWeight="Bold"/>
                <TextBlock Grid.Row="2" Grid.Column="2" 
                           Text="{Binding AuthorizedFeaturesText}"
                           TextWrapping="Wrap"/>

                <CheckBox Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3"
                          Content="License验证成功"
                          IsChecked="{Binding IsLicenseValid}"
                          IsEnabled="False"/>
            </Grid>
        </GroupBox>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="8" 
                    Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Content="继续使用"
                    Width="80"
                    Height="30"
                    Margin="0,0,10,0"
                    Command="{Binding ProceedToApplicationCommand}"/>
            
            <Button Content="退出"
                    Width="80"
                    Height="30"
                    Command="{Binding ExitApplicationCommand}"/>
        </StackPanel>
    </Grid>
</Window>
