# License验证失败Bug修复测试指南

## 修复内容说明

### 问题分析
1. **MessageBox阻塞问题**: 原代码在`licenseView.Show()`后立即调用`MessageBox.Show()`，导致UI线程阻塞
2. **异步上下文问题**: 在异步方法中调用同步UI操作可能导致线程问题
3. **窗口生命周期问题**: License验证窗口可能没有正确显示或被意外关闭

### 修复方案
1. **使用Dispatcher.Invoke**: 确保UI操作在正确的线程上执行
2. **改进MessageBox显示时机**: 使用窗口Activated事件和BeginInvoke确保窗口完全显示后再显示错误对话框
3. **增强错误处理**: 添加try-catch块和更好的异常处理
4. **窗口焦点管理**: 确保窗口正确获得焦点和激活状态

## 测试场景

### 测试场景1：License文件不存在
**测试步骤**：
1. 确保AirMonitor程序目录下没有license.lic文件
2. 运行命令：`dotnet run --project AirMonitor`
3. 观察启动流程

**预期结果**：
1. 显示启动加载界面："正在启动 AirMonitor..."
2. 显示："正在初始化应用程序..."
3. 显示："正在检查License..."
4. 显示："License验证失败" + 错误信息
5. 自动关闭加载界面
6. **正常显示License验证界面**
7. **弹出详细错误对话框**："在默认路径下未找到License文件..."
8. **点击"确定"后，License验证界面应该保持显示**
9. 用户可以在验证界面中获取设备指纹或选择License文件

### 测试场景2：License文件存在但验证失败（设备不匹配）
**测试步骤**：
1. 将一个其他设备的License文件放置到程序目录下，命名为license.lic
2. 运行命令：`dotnet run --project AirMonitor`
3. 观察启动流程

**预期结果**：
1. 显示启动加载界面和检查过程
2. 显示："License验证失败" + 设备不匹配错误
3. 自动关闭加载界面
4. **正常显示License验证界面**
5. **界面中自动填入license.lic文件路径**
6. **弹出详细错误对话框**："设备指纹不匹配..."
7. **点击"确定"后，License验证界面应该保持显示**
8. 用户可以获取当前设备指纹或重新选择License文件

### 测试场景3：License文件过期
**测试步骤**：
1. 使用注册机生成一个已过期的License文件
2. 将文件放置到程序目录下
3. 运行主程序

**预期结果**：
1. 启动流程正常进行到License检查
2. 显示过期错误信息
3. **正常进入License验证界面**
4. **弹出过期详细说明对话框**
5. **点击"确定"后界面保持显示**

### 测试场景4：License文件损坏
**测试步骤**：
1. 创建一个无效的license.lic文件（随意内容）
2. 运行主程序

**预期结果**：
1. 检测到文件格式错误
2. **正常进入License验证界面**
3. **显示文件损坏的详细错误信息**
4. **界面保持可用状态**

## 验证要点

### 关键验证点
1. **License验证界面必须正常显示**
2. **错误对话框点击"确定"后，程序不能退出**
3. **License验证界面必须保持可用状态**
4. **用户可以在界面中进行后续操作**

### 界面功能验证
在License验证界面中验证以下功能：
1. **"获取当前设备指纹"按钮可用**
2. **"复制指纹"按钮在获取指纹后可用**
3. **"浏览..."按钮可以选择其他License文件**
4. **"验证License"按钮可以重新验证**
5. **"继续使用"按钮在验证成功后可用**
6. **"退出"按钮可以正常退出程序**

## 测试命令

```bash
# 测试场景1：删除License文件
rm -f AirMonitor/bin/Debug/net8.0-windows/license.lic
dotnet run --project AirMonitor

# 测试场景2：使用错误的License文件
echo "invalid license content" > AirMonitor/bin/Debug/net8.0-windows/license.lic
dotnet run --project AirMonitor

# 测试场景3：生成测试License（需要先运行注册机）
dotnet run --project AirMonitor.LicenseGenerator
# 在注册机中生成一个过期的License文件

# 测试场景4：创建损坏的License文件
echo "corrupted data" > AirMonitor/bin/Debug/net8.0-windows/license.lic
dotnet run --project AirMonitor
```

## 修复验证

如果修复成功，应该观察到：
1. **所有测试场景都能正常进入License验证界面**
2. **错误对话框不会导致程序退出**
3. **用户可以在验证界面中进行所有预期操作**
4. **界面切换流畅，无异常退出**

如果仍有问题，请检查：
1. 控制台是否有异常输出
2. License验证界面是否正确显示
3. 错误对话框的内容是否正确
4. 窗口焦点和激活状态是否正常
