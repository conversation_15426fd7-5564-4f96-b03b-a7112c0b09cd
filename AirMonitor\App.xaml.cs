﻿using AirMonitor.Services;
using AirMonitor.ViewModels;
using AirMonitor.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Windows;

namespace AirMonitor
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置依赖注入
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // 注册服务
                    services.AddSingleton<LicenseService>();

                    // 注册ViewModels
                    services.AddTransient<LicenseViewModel>();

                    // 注册Views
                    services.AddTransient<LicenseView>();
                    services.AddTransient<MainWindow>();
                })
                .Build();

            // 启动License验证窗口
            var licenseViewModel = _host.Services.GetRequiredService<LicenseViewModel>();
            var licenseView = new LicenseView(licenseViewModel);
            licenseView.Show();

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }
    }

}
