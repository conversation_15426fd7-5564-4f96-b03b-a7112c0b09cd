﻿using AirMonitor.Services;
using AirMonitor.ViewModels;
using AirMonitor.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.IO;
using System.Threading.Tasks;
using System.Windows;

namespace AirMonitor
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // 配置依赖注入
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // 注册服务
                    services.AddSingleton<LicenseService>();

                    // 注册ViewModels
                    services.AddTransient<LicenseViewModel>();

                    // 注册Views
                    services.AddTransient<LicenseView>();
                    services.AddTransient<MainWindow>();
                })
                .Build();

            // 自动检查License
            await CheckAndHandleLicenseAsync();

            base.OnStartup(e);
        }

        /// <summary>
        /// 检查并处理License验证
        /// </summary>
        private async Task CheckAndHandleLicenseAsync()
        {
            var licenseService = _host!.Services.GetRequiredService<LicenseService>();
            var defaultLicensePath = licenseService.GetDefaultLicensePath();

            // 检查默认路径下是否存在License文件
            if (File.Exists(defaultLicensePath))
            {
                // 尝试验证License
                var licenseInfo = await licenseService.ValidateLicenseAsync(defaultLicensePath);

                if (licenseInfo.IsValid)
                {
                    // License有效，直接进入主程序
                    ShowMainWindow();
                    return;
                }
                else
                {
                    // License无效，显示错误信息并要求重新选择
                    var result = MessageBox.Show(
                        $"License验证失败：{licenseInfo.StatusMessage}\n\n是否重新选择License文件？",
                        "License验证失败",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.No)
                    {
                        Shutdown();
                        return;
                    }
                }
            }

            // License文件不存在或验证失败，显示License验证界面
            ShowLicenseView();
        }

        /// <summary>
        /// 显示License验证界面
        /// </summary>
        private void ShowLicenseView()
        {
            var licenseViewModel = _host!.Services.GetRequiredService<LicenseViewModel>();
            var licenseView = new LicenseView(licenseViewModel);

            // 订阅License验证成功事件
            licenseViewModel.LicenseValidated += OnLicenseValidated;

            licenseView.Show();
        }

        /// <summary>
        /// 显示主窗口
        /// </summary>
        private void ShowMainWindow()
        {
            var mainWindow = _host!.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        /// <summary>
        /// License验证成功事件处理
        /// </summary>
        private void OnLicenseValidated(object? sender, EventArgs e)
        {
            if (sender is LicenseViewModel viewModel)
            {
                viewModel.LicenseValidated -= OnLicenseValidated;

                // 关闭License验证窗口
                foreach (Window window in Windows)
                {
                    if (window is LicenseView)
                    {
                        window.Close();
                        break;
                    }
                }

                // 显示主窗口
                ShowMainWindow();
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }
    }

}
