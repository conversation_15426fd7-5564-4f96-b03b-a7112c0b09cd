﻿using AirMonitor.Services;
using AirMonitor.ViewModels;
using AirMonitor.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace AirMonitor
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;
        private StartupLoadingView? _loadingView;
        private StartupLoadingViewModel? _loadingViewModel;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // 配置依赖注入
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // 注册服务
                    services.AddSingleton<LicenseService>();
                    services.AddSingleton<StartupService>();

                    // 注册ViewModels
                    services.AddTransient<LicenseViewModel>();
                    services.AddTransient<StartupLoadingViewModel>();

                    // 注册Views
                    services.AddTransient<LicenseView>();
                    services.AddTransient<StartupLoadingView>();
                    services.AddTransient<MainWindow>();
                })
                .Build();

            // 显示启动加载界面
            ShowStartupLoadingView();

            // 执行异步启动流程
            await PerformStartupSequenceAsync();

            base.OnStartup(e);
        }

        /// <summary>
        /// 显示启动加载界面
        /// </summary>
        private void ShowStartupLoadingView()
        {
            _loadingViewModel = _host!.Services.GetRequiredService<StartupLoadingViewModel>();
            _loadingView = new StartupLoadingView(_loadingViewModel);
            _loadingView.Show();
        }

        /// <summary>
        /// 执行启动序列
        /// </summary>
        private async Task PerformStartupSequenceAsync()
        {
            try
            {
                // 更新状态：初始化应用程序
                _loadingViewModel?.UpdateStatus("正在初始化应用程序...", "加载系统组件", 10);
                await Task.Delay(500); // 模拟初始化时间

                // 更新状态：检查License
                _loadingViewModel?.UpdateStatus("正在检查License...", "验证授权信息", 30);
                await Task.Delay(300);

                // 执行License检查
                var startupService = _host!.Services.GetRequiredService<StartupService>();
                var licenseCheckResult = await startupService.CheckLicenseOnStartupAsync();

                if (licenseCheckResult.IsValid)
                {
                    // License验证成功
                    _loadingViewModel?.UpdateStatus("License验证成功", "正在启动主程序...", 80);
                    await Task.Delay(500);

                    _loadingViewModel?.SetCompleted("启动完成");
                    await Task.Delay(300);

                    // 关闭加载界面，显示主窗口
                    CloseStartupLoadingView();

                    // 使用Dispatcher确保在UI线程上执行
                    Dispatcher.Invoke(() => ShowMainWindow());
                }
                else
                {
                    // License验证失败
                    _loadingViewModel?.SetError("License验证失败", licenseCheckResult.ErrorMessage);
                    await Task.Delay(1000);

                    // 关闭加载界面，显示License验证界面
                    CloseStartupLoadingView();

                    // 使用Dispatcher确保在UI线程上执行
                    Dispatcher.Invoke(() => ShowLicenseViewWithError(licenseCheckResult));
                }
            }
            catch (Exception ex)
            {
                // 启动过程中发生异常
                _loadingViewModel?.SetError("启动失败", $"启动过程中发生错误：{ex.Message}");
                await Task.Delay(2000);

                // 关闭加载界面
                CloseStartupLoadingView();

                // 使用Dispatcher确保在UI线程上显示错误对话框
                Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"应用程序启动失败：{ex.Message}", "启动错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                });
            }
        }

        /// <summary>
        /// 关闭启动加载界面
        /// </summary>
        private void CloseStartupLoadingView()
        {
            _loadingView?.Close();
            _loadingView = null;
            _loadingViewModel = null;
        }

        /// <summary>
        /// 显示License验证界面（带错误信息）
        /// </summary>
        /// <param name="checkResult">License检查结果</param>
        private void ShowLicenseViewWithError(StartupService.LicenseCheckResult checkResult)
        {
            try
            {
                var licenseViewModel = _host!.Services.GetRequiredService<LicenseViewModel>();
                var licenseView = new LicenseView(licenseViewModel);

                // 设置初始错误信息
                licenseViewModel.SetInitialStatus(checkResult.ErrorMessage);

                // 如果文件存在但验证失败，自动设置文件路径
                if (checkResult.FileExists)
                {
                    licenseViewModel.LicenseFilePath = checkResult.FilePath;
                }

                // 订阅License验证成功事件
                licenseViewModel.LicenseValidated += OnLicenseValidated;

                // 先显示License验证界面
                licenseView.Show();

                // 确保窗口已显示后再显示错误对话框
                licenseView.Activated += (sender, e) =>
                {
                    // 只在第一次激活时显示错误信息
                    if (!string.IsNullOrEmpty(checkResult.DetailedMessage))
                    {
                        // 使用BeginInvoke确保在窗口完全显示后显示MessageBox
                        Dispatcher.BeginInvoke(new Action(() =>
                        {
                            MessageBox.Show(licenseView, checkResult.DetailedMessage, "License验证失败",
                                          MessageBoxButton.OK, MessageBoxImage.Warning);
                        }), System.Windows.Threading.DispatcherPriority.ApplicationIdle);

                        // 清空详细消息，避免重复显示
                        checkResult.DetailedMessage = string.Empty;
                    }
                };

                // 确保窗口获得焦点
                licenseView.Activate();
                licenseView.Focus();
            }
            catch (Exception ex)
            {
                // 如果显示License验证界面失败，显示错误并退出
                MessageBox.Show($"无法显示License验证界面：{ex.Message}\n\n应用程序将退出。",
                              "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        /// <summary>
        /// 显示License验证界面（无错误信息）
        /// </summary>
        private void ShowLicenseView()
        {
            var licenseViewModel = _host!.Services.GetRequiredService<LicenseViewModel>();
            var licenseView = new LicenseView(licenseViewModel);

            // 订阅License验证成功事件
            licenseViewModel.LicenseValidated += OnLicenseValidated;

            licenseView.Show();
        }

        /// <summary>
        /// 显示主窗口
        /// </summary>
        private void ShowMainWindow()
        {
            try
            {
                var mainWindow = _host!.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();
                mainWindow.Activate();
                mainWindow.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法显示主窗口：{ex.Message}\n\n应用程序将退出。",
                              "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        /// <summary>
        /// License验证成功事件处理
        /// </summary>
        private void OnLicenseValidated(object? sender, EventArgs e)
        {
            if (sender is LicenseViewModel viewModel)
            {
                viewModel.LicenseValidated -= OnLicenseValidated;

                // 关闭License验证窗口
                foreach (Window window in Windows)
                {
                    if (window is LicenseView)
                    {
                        window.Close();
                        break;
                    }
                }

                // 显示主窗口
                ShowMainWindow();
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }
    }

}
