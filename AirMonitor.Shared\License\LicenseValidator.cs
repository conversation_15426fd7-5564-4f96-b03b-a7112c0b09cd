using AirMonitor.Shared.Constants;
using AirMonitor.Shared.Models;
using AirMonitor.Shared.Utils;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace AirMonitor.Shared.License
{
    /// <summary>
    /// License验证器
    /// </summary>
    public class LicenseValidator
    {
        /// <summary>
        /// License验证结果
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public string ErrorCode { get; set; } = string.Empty;
            public string ErrorMessage { get; set; } = string.Empty;
            public LicenseModel? License { get; set; }
        }

        /// <summary>
        /// 验证License文件
        /// </summary>
        /// <param name="licenseFilePath">License文件路径</param>
        /// <returns>验证结果</returns>
        public async Task<ValidationResult> ValidateLicenseAsync(string licenseFilePath)
        {
            var result = new ValidationResult();

            try
            {
                // 1. 检查文件是否存在
                if (!File.Exists(licenseFilePath))
                {
                    result.ErrorCode = LicenseConstants.ErrorCodes.FILE_NOT_FOUND;
                    result.ErrorMessage = "License文件不存在";
                    return result;
                }

                // 2. 读取并解密License文件
                var licenseContent = await ReadLicenseFileAsync(licenseFilePath);
                if (string.IsNullOrEmpty(licenseContent))
                {
                    result.ErrorCode = LicenseConstants.ErrorCodes.DECRYPTION_FAILED;
                    result.ErrorMessage = "License文件解密失败";
                    return result;
                }

                // 3. 反序列化License数据
                var license = DeserializeLicense(licenseContent);
                if (license == null)
                {
                    result.ErrorCode = LicenseConstants.ErrorCodes.INVALID_FORMAT;
                    result.ErrorMessage = "License文件格式无效";
                    return result;
                }

                // 4. 验证License签名
                if (!await ValidateLicenseSignatureAsync(license))
                {
                    result.ErrorCode = LicenseConstants.ErrorCodes.SIGNATURE_INVALID;
                    result.ErrorMessage = "License签名验证失败";
                    return result;
                }

                // 5. 验证设备指纹
                if (!await ValidateDeviceFingerprintAsync(license))
                {
                    result.ErrorCode = LicenseConstants.ErrorCodes.DEVICE_MISMATCH;
                    result.ErrorMessage = "设备指纹不匹配";
                    return result;
                }

                // 6. 验证有效期
                if (!license.IsValidPeriod())
                {
                    result.ErrorCode = LicenseConstants.ErrorCodes.EXPIRED;
                    result.ErrorMessage = "License已过期";
                    return result;
                }

                // 验证成功
                result.IsValid = true;
                result.License = license;
                return result;
            }
            catch (Exception ex)
            {
                result.ErrorCode = LicenseConstants.ErrorCodes.CORRUPTED;
                result.ErrorMessage = $"License验证过程中发生错误: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 读取License文件内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>解密后的License内容</returns>
        private async Task<string> ReadLicenseFileAsync(string filePath)
        {
            try
            {
                var encryptedContent = await File.ReadAllTextAsync(filePath);
                return EncryptionHelper.DecryptString(encryptedContent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取License文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 反序列化License数据
        /// </summary>
        /// <param name="licenseContent">License JSON内容</param>
        /// <returns>License模型</returns>
        private LicenseModel? DeserializeLicense(string licenseContent)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };

                return JsonSerializer.Deserialize<LicenseModel>(licenseContent, options);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"反序列化License失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证License签名
        /// </summary>
        /// <param name="license">License模型</param>
        /// <returns>签名是否有效</returns>
        private async Task<bool> ValidateLicenseSignatureAsync(LicenseModel license)
        {
            try
            {
                // 生成用于签名的数据字符串
                var signatureData = GenerateSignatureData(license);
                
                // 计算签名
                var computedSignature = EncryptionHelper.ComputeSHA256Hash(signatureData);
                
                // 验证签名
                return computedSignature.Equals(license.Signature, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证License签名失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证设备指纹
        /// </summary>
        /// <param name="license">License模型</param>
        /// <returns>设备指纹是否匹配</returns>
        private async Task<bool> ValidateDeviceFingerprintAsync(LicenseModel license)
        {
            try
            {
                return await DeviceFingerprint.ValidateDeviceFingerprintAsync(license.DeviceFingerprint);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证设备指纹失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成用于签名的数据字符串
        /// </summary>
        /// <param name="license">License模型</param>
        /// <returns>签名数据字符串</returns>
        private string GenerateSignatureData(LicenseModel license)
        {
            // 将关键字段组合成签名字符串
            var signatureFields = new[]
            {
                license.LicenseId,
                license.LicenseType,
                license.DeviceFingerprint,
                license.ValidFrom.ToString("yyyy-MM-dd"),
                license.ValidTo?.ToString("yyyy-MM-dd") ?? "PERMANENT",
                string.Join(",", license.AuthorizedFeatures),
                string.Join(",", license.AuthorizedViews),
                string.Join(",", license.AuthorizedButtons)
            };

            return string.Join("|", signatureFields);
        }
    }
}
