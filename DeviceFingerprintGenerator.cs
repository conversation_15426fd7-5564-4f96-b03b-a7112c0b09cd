using AirMonitor.Shared.Utils;
using System;
using System.Threading.Tasks;

/// <summary>
/// 设备指纹生成工具 - 用于测试和获取当前设备指纹
/// 编译命令: dotnet run DeviceFingerprintGenerator.cs
/// </summary>
class DeviceFingerprintGenerator
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== AirMonitor 设备指纹生成工具 ===");
        Console.WriteLine();

        try
        {
            Console.WriteLine("正在获取设备信息...");
            var deviceInfo = await DeviceFingerprint.GetDeviceInfoAsync();

            Console.WriteLine("设备详细信息:");
            Console.WriteLine($"  计算机名: {deviceInfo.ComputerName}");
            Console.WriteLine($"  操作系统: {deviceInfo.OSVersion}");
            Console.WriteLine($"  主板序列号: {deviceInfo.MotherboardSerial}");
            Console.WriteLine($"  CPU序列号: {deviceInfo.CpuSerial}");
            Console.WriteLine($"  硬盘序列号: {deviceInfo.HardDiskSerial}");
            Console.WriteLine($"  MAC地址: {deviceInfo.MacAddress}");
            Console.WriteLine();

            var fingerprint = deviceInfo.GenerateFingerprint();
            Console.WriteLine("生成的设备指纹:");
            Console.WriteLine($"  {fingerprint}");
            Console.WriteLine();

            Console.WriteLine("指纹信息:");
            Console.WriteLine($"  长度: {fingerprint.Length} 字符");
            Console.WriteLine($"  格式: Base64编码");
            Console.WriteLine();

            Console.WriteLine("使用说明:");
            Console.WriteLine("1. 复制上面的设备指纹字符串");
            Console.WriteLine("2. 在License生成器中粘贴到设备指纹输入框");
            Console.WriteLine("3. 生成对应的License文件");
            Console.WriteLine();

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
