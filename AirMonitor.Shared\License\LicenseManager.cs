using AirMonitor.Shared.Constants;
using AirMonitor.Shared.Models;
using AirMonitor.Shared.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace AirMonitor.Shared.License
{
    /// <summary>
    /// License管理器
    /// </summary>
    public class LicenseManager
    {
        private readonly LicenseValidator _validator;
        private LicenseModel? _currentLicense;
        private bool _isLicenseLoaded = false;

        public LicenseManager()
        {
            _validator = new LicenseValidator();
        }

        /// <summary>
        /// 当前License信息
        /// </summary>
        public LicenseModel? CurrentLicense => _currentLicense;

        /// <summary>
        /// License是否已加载
        /// </summary>
        public bool IsLicenseLoaded => _isLicenseLoaded;

        /// <summary>
        /// License是否有效
        /// </summary>
        public bool IsLicenseValid => _currentLicense != null && _currentLicense.IsValidPeriod();

        /// <summary>
        /// 加载并验证License文件
        /// </summary>
        /// <param name="licenseFilePath">License文件路径</param>
        /// <returns>验证结果</returns>
        public async Task<LicenseValidator.ValidationResult> LoadLicenseAsync(string licenseFilePath)
        {
            var result = await _validator.ValidateLicenseAsync(licenseFilePath);
            
            if (result.IsValid && result.License != null)
            {
                _currentLicense = result.License;
                _isLicenseLoaded = true;
            }
            else
            {
                _currentLicense = null;
                _isLicenseLoaded = false;
            }

            return result;
        }

        /// <summary>
        /// 生成License文件
        /// </summary>
        /// <param name="license">License模型</param>
        /// <param name="outputFilePath">输出文件路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> GenerateLicenseFileAsync(LicenseModel license, string outputFilePath)
        {
            try
            {
                // 生成签名
                license.Signature = GenerateLicenseSignature(license);

                // 序列化为JSON
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };

                var jsonContent = JsonSerializer.Serialize(license, options);

                // 加密并保存到文件
                var encryptedContent = EncryptionHelper.EncryptString(jsonContent);
                await File.WriteAllTextAsync(outputFilePath, encryptedContent);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成License文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查功能是否被授权
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <returns>是否被授权</returns>
        public bool IsFeatureAuthorized(string featureName)
        {
            if (!IsLicenseValid) return false;
            return _currentLicense!.IsFeatureAuthorized(featureName);
        }

        /// <summary>
        /// 检查界面是否被授权
        /// </summary>
        /// <param name="viewName">界面名称</param>
        /// <returns>是否被授权</returns>
        public bool IsViewAuthorized(string viewName)
        {
            if (!IsLicenseValid) return false;
            return _currentLicense!.IsViewAuthorized(viewName);
        }

        /// <summary>
        /// 检查按钮是否被授权
        /// </summary>
        /// <param name="buttonName">按钮名称</param>
        /// <returns>是否被授权</returns>
        public bool IsButtonAuthorized(string buttonName)
        {
            if (!IsLicenseValid) return false;
            return _currentLicense!.IsButtonAuthorized(buttonName);
        }

        /// <summary>
        /// 获取License详细信息
        /// </summary>
        /// <returns>License信息字典</returns>
        public Dictionary<string, object> GetLicenseInfo()
        {
            var info = new Dictionary<string, object>();

            if (_currentLicense == null)
            {
                info["Status"] = "未加载License";
                return info;
            }

            info["LicenseId"] = _currentLicense.LicenseId;
            info["LicenseType"] = _currentLicense.LicenseType;
            info["CustomerName"] = _currentLicense.CustomerName;
            info["IssuedAt"] = _currentLicense.IssuedAt.ToString("yyyy-MM-dd HH:mm:ss");
            info["ValidFrom"] = _currentLicense.ValidFrom.ToString("yyyy-MM-dd");
            info["ValidTo"] = _currentLicense.ValidTo?.ToString("yyyy-MM-dd") ?? "永久有效";
            info["RemainingDays"] = _currentLicense.GetRemainingDays()?.ToString() ?? "永久有效";
            info["AuthorizedFeatures"] = _currentLicense.AuthorizedFeatures;
            info["AuthorizedViews"] = _currentLicense.AuthorizedViews;
            info["AuthorizedButtons"] = _currentLicense.AuthorizedButtons;
            info["MaxConcurrentUsers"] = _currentLicense.MaxConcurrentUsers;

            return info;
        }

        /// <summary>
        /// 创建普通License模板
        /// </summary>
        /// <param name="deviceFingerprint">设备指纹</param>
        /// <param name="customerName">客户名称</param>
        /// <param name="validDays">有效天数（null表示永久有效）</param>
        /// <returns>License模板</returns>
        public static LicenseModel CreateNormalLicense(string deviceFingerprint, string customerName, int? validDays = null)
        {
            var license = new LicenseModel
            {
                LicenseType = LicenseConstants.LicenseTypes.NORMAL,
                CustomerName = customerName,
                DeviceFingerprint = deviceFingerprint,
                ValidFrom = DateTime.Now,
                ValidTo = validDays.HasValue ? DateTime.Now.AddDays(validDays.Value) : null
            };

            // 添加普通功能权限
            license.AuthorizedFeatures.AddRange(new[]
            {
                LicenseConstants.Features.BASIC_MONITORING,
                LicenseConstants.Features.DATA_EXPORT,
                LicenseConstants.Features.SYSTEM_SETTINGS
            });

            return license;
        }

        /// <summary>
        /// 创建管理License模板
        /// </summary>
        /// <param name="deviceFingerprint">设备指纹</param>
        /// <param name="customerName">客户名称</param>
        /// <param name="validDays">有效天数（null表示永久有效）</param>
        /// <returns>License模板</returns>
        public static LicenseModel CreateManagementLicense(string deviceFingerprint, string customerName, int? validDays = null)
        {
            var license = CreateNormalLicense(deviceFingerprint, customerName, validDays);
            license.LicenseType = LicenseConstants.LicenseTypes.MANAGEMENT;

            // 添加管理功能权限
            license.AuthorizedFeatures.AddRange(new[]
            {
                LicenseConstants.Features.ADVANCED_MONITORING,
                LicenseConstants.Features.REPORT_GENERATION,
                LicenseConstants.Features.DEVICE_CONTROL,
                LicenseConstants.Features.ALARM_MANAGEMENT,
                LicenseConstants.Features.USER_MANAGEMENT
            });

            return license;
        }

        /// <summary>
        /// 生成License签名
        /// </summary>
        /// <param name="license">License模型</param>
        /// <returns>签名字符串</returns>
        private string GenerateLicenseSignature(LicenseModel license)
        {
            var signatureFields = new[]
            {
                license.LicenseId,
                license.LicenseType,
                license.DeviceFingerprint,
                license.ValidFrom.ToString("yyyy-MM-dd"),
                license.ValidTo?.ToString("yyyy-MM-dd") ?? "PERMANENT",
                string.Join(",", license.AuthorizedFeatures),
                string.Join(",", license.AuthorizedViews),
                string.Join(",", license.AuthorizedButtons)
            };

            var signatureData = string.Join("|", signatureFields);
            return EncryptionHelper.ComputeSHA256Hash(signatureData);
        }
    }
}
