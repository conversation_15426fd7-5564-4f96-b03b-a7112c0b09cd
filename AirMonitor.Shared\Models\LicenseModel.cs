using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace AirMonitor.Shared.Models
{
    /// <summary>
    /// License数据模型
    /// </summary>
    public class LicenseModel
    {
        /// <summary>
        /// License版本
        /// </summary>
        [JsonPropertyName("version")]
        public string Version { get; set; } = Constants.LicenseConstants.LICENSE_VERSION;

        /// <summary>
        /// License唯一标识
        /// </summary>
        [JsonPropertyName("licenseId")]
        public string LicenseId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// License类型
        /// </summary>
        [JsonPropertyName("licenseType")]
        public string LicenseType { get; set; } = Constants.LicenseConstants.LicenseTypes.NORMAL;

        /// <summary>
        /// 客户名称
        /// </summary>
        [JsonPropertyName("customerName")]
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 客户邮箱
        /// </summary>
        [JsonPropertyName("customerEmail")]
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// 设备指纹
        /// </summary>
        [JsonPropertyName("deviceFingerprint")]
        public string DeviceFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// License生成时间
        /// </summary>
        [JsonPropertyName("issuedAt")]
        public DateTime IssuedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// License有效期开始时间
        /// </summary>
        [JsonPropertyName("validFrom")]
        public DateTime ValidFrom { get; set; } = DateTime.Now;

        /// <summary>
        /// License有效期结束时间（null表示永久有效）
        /// </summary>
        [JsonPropertyName("validTo")]
        public DateTime? ValidTo { get; set; }

        /// <summary>
        /// 授权的功能列表
        /// </summary>
        [JsonPropertyName("authorizedFeatures")]
        public List<string> AuthorizedFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 授权的界面列表
        /// </summary>
        [JsonPropertyName("authorizedViews")]
        public List<string> AuthorizedViews { get; set; } = new List<string>();

        /// <summary>
        /// 授权的按钮列表
        /// </summary>
        [JsonPropertyName("authorizedButtons")]
        public List<string> AuthorizedButtons { get; set; } = new List<string>();

        /// <summary>
        /// 最大并发用户数
        /// </summary>
        [JsonPropertyName("maxConcurrentUsers")]
        public int MaxConcurrentUsers { get; set; } = 1;

        /// <summary>
        /// 自定义属性
        /// </summary>
        [JsonPropertyName("customProperties")]
        public Dictionary<string, string> CustomProperties { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 数字签名
        /// </summary>
        [JsonPropertyName("signature")]
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// 检查License是否在有效期内
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValidPeriod()
        {
            var now = DateTime.Now;
            return now >= ValidFrom && (ValidTo == null || now <= ValidTo);
        }

        /// <summary>
        /// 检查是否授权指定功能
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <returns>是否授权</returns>
        public bool IsFeatureAuthorized(string featureName)
        {
            return AuthorizedFeatures.Contains(featureName);
        }

        /// <summary>
        /// 检查是否授权指定界面
        /// </summary>
        /// <param name="viewName">界面名称</param>
        /// <returns>是否授权</returns>
        public bool IsViewAuthorized(string viewName)
        {
            return AuthorizedViews.Contains(viewName);
        }

        /// <summary>
        /// 检查是否授权指定按钮
        /// </summary>
        /// <param name="buttonName">按钮名称</param>
        /// <returns>是否授权</returns>
        public bool IsButtonAuthorized(string buttonName)
        {
            return AuthorizedButtons.Contains(buttonName);
        }

        /// <summary>
        /// 获取License剩余天数
        /// </summary>
        /// <returns>剩余天数，null表示永久有效</returns>
        public int? GetRemainingDays()
        {
            if (ValidTo == null) return null;
            
            var remaining = ValidTo.Value - DateTime.Now;
            return remaining.Days > 0 ? remaining.Days : 0;
        }
    }
}
