<Window x:Class="AirMonitor.LicenseGenerator.Views.LicenseGeneratorView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:AirMonitor.LicenseGenerator.ViewModels"
        mc:Ignorable="d"
        Title="AirMonitor License生成器"
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="10"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="10"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="10"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="AirMonitor License生成器"
                   FontSize="20"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"/>

        <!-- 主要内容区域 -->
        <TabControl Grid.Row="2">
            
            <!-- 基本信息标签页 -->
            <TabItem Header="基本信息">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        
                        <!-- License类型 -->
                        <GroupBox Header="License类型" Margin="0,0,0,10">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="类型:" VerticalAlignment="Center"/>
                                <ComboBox Grid.Column="2"
                                         ItemsSource="{Binding LicenseTypes}"
                                         SelectedItem="{Binding SelectedLicenseType}"/>
                            </Grid>
                        </GroupBox>

                        <!-- 设备指纹 -->
                        <GroupBox Header="设备指纹" Margin="0,0,0,10">
                            <StackPanel Margin="10">
                                <TextBlock Text="设备指纹:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding DeviceFingerprint, UpdateSourceTrigger=PropertyChanged}"
                                         TextWrapping="Wrap"
                                         Height="60"
                                         VerticalScrollBarVisibility="Auto"
                                         AcceptsReturn="True"/>

                                <TextBlock Text="{Binding DeviceFingerprintHint}"
                                           FontSize="11"
                                           Margin="0,5,0,0"
                                           TextWrapping="Wrap">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Foreground" Value="Gray"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsDeviceFingerprintValid}" Value="True">
                                                    <Setter Property="Foreground" Value="Green"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsDeviceFingerprintValid}" Value="False">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>
                        </GroupBox>

                        <!-- 有效期设置 -->
                        <GroupBox Header="有效期设置" Margin="0,0,0,10">
                            <StackPanel Margin="10">
                                <RadioButton Content="永久有效" IsChecked="{Binding IsPermanent}" Margin="0,0,0,10"/>
                                <StackPanel Orientation="Horizontal">
                                    <RadioButton VerticalAlignment="Center">
                                        <RadioButton.Style>
                                            <Style TargetType="RadioButton">
                                                <Setter Property="IsChecked" Value="False"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsPermanent}" Value="False">
                                                        <Setter Property="IsChecked" Value="True"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </RadioButton.Style>
                                        指定天数:
                                    </RadioButton>
                                    <TextBox Text="{Binding ValidDays}" Width="80" Margin="10,0,0,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="天" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>


                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 功能权限标签页 -->
            <TabItem Header="功能权限">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="10"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <Button Content="全选" Command="{Binding SelectAllFeaturesCommand}" Margin="0,0,10,0"/>
                        <Button Content="取消全选" Command="{Binding UnselectAllFeaturesCommand}"/>
                    </StackPanel>

                    <ListBox Grid.Row="2" ItemsSource="{Binding AvailableFeatures}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <CheckBox IsChecked="{Binding IsSelected}" Margin="5">
                                    <StackPanel>
                                        <TextBlock Text="{Binding DisplayName}" FontWeight="Bold"/>
                                        <TextBlock Text="{Binding Description}" FontSize="11" Foreground="Gray"/>
                                    </StackPanel>
                                </CheckBox>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </TabItem>

            <!-- 界面权限标签页 -->
            <TabItem Header="界面权限">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="10"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <Button Content="全选" Command="{Binding SelectAllViewsCommand}" Margin="0,0,10,0"/>
                        <Button Content="取消全选" Command="{Binding UnselectAllViewsCommand}"/>
                    </StackPanel>

                    <ListBox Grid.Row="2" ItemsSource="{Binding AvailableViews}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <CheckBox IsChecked="{Binding IsSelected}" Margin="5">
                                    <StackPanel>
                                        <TextBlock Text="{Binding DisplayName}" FontWeight="Bold"/>
                                        <TextBlock Text="{Binding Description}" FontSize="11" Foreground="Gray"/>
                                    </StackPanel>
                                </CheckBox>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </TabItem>

            <!-- 按钮权限标签页 -->
            <TabItem Header="按钮权限">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="10"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <Button Content="全选" Command="{Binding SelectAllButtonsCommand}" Margin="0,0,10,0"/>
                        <Button Content="取消全选" Command="{Binding UnselectAllButtonsCommand}"/>
                    </StackPanel>

                    <ListBox Grid.Row="2" ItemsSource="{Binding AvailableButtons}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <CheckBox IsChecked="{Binding IsSelected}" Margin="5">
                                    <StackPanel>
                                        <TextBlock Text="{Binding DisplayName}" FontWeight="Bold"/>
                                        <TextBlock Text="{Binding Description}" FontSize="11" Foreground="Gray"/>
                                        <TextBlock Text="{Binding ViewName, StringFormat='所属界面: {0}'}" FontSize="10" Foreground="Blue"/>
                                    </StackPanel>
                                </CheckBox>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- 输出文件设置 -->
        <GroupBox Grid.Row="4" Header="输出设置">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" Text="{Binding OutputFilePath}" IsReadOnly="True"/>
                <Button Grid.Column="2" Content="浏览..." Width="80" Command="{Binding SelectOutputPathCommand}"/>
            </Grid>
        </GroupBox>

        <!-- 状态和操作按钮 -->
        <Grid Grid.Row="6">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
            
            <Button Grid.Column="1" 
                    Content="生成License"
                    Width="120"
                    Height="35"
                    Command="{Binding GenerateLicenseCommand}">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="IsEnabled" Value="True"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsGenerating}" Value="True">
                                <Setter Property="IsEnabled" Value="False"/>
                                <Setter Property="Content" Value="生成中..."/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Grid>
</Window>
