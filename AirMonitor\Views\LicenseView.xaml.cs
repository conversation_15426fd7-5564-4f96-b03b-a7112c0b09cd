using AirMonitor.ViewModels;
using System.Windows;

namespace AirMonitor.Views
{
    /// <summary>
    /// LicenseView.xaml 的交互逻辑
    /// </summary>
    public partial class LicenseView : Window
    {
        public LicenseView()
        {
            InitializeComponent();
        }

        public LicenseView(LicenseViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }
    }
}
