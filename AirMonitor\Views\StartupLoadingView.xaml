<Window x:Class="AirMonitor.Views.StartupLoadingView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="AirMonitor - 启动中"
        Height="300" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Border Background="White" 
            BorderBrush="LightGray" 
            BorderThickness="1" 
            CornerRadius="10">
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="20"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="20"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 应用程序标题 -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center">
                <TextBlock Text="AirMonitor" 
                           FontSize="24" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center"/>
                <TextBlock Text="空调监控调试软件" 
                           FontSize="14" 
                           Foreground="Gray" 
                           HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- 状态信息 -->
            <TextBlock Grid.Row="2" 
                       Text="{Binding StatusMessage}" 
                       FontSize="14" 
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"/>

            <!-- 进度条 -->
            <ProgressBar Grid.Row="4" 
                         Height="20" 
                         IsIndeterminate="{Binding IsLoading}"
                         Value="{Binding ProgressValue}"
                         Maximum="100">
                <ProgressBar.Style>
                    <Style TargetType="ProgressBar">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsLoading}" Value="False">
                                <Setter Property="IsIndeterminate" Value="False"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ProgressBar.Style>
            </ProgressBar>

            <!-- 详细信息 -->
            <TextBlock Grid.Row="6" 
                       Text="{Binding DetailMessage}" 
                       FontSize="11" 
                       Foreground="Gray" 
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"
                       TextAlignment="Center">
                <TextBlock.Style>
                    <Style TargetType="TextBlock">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ShowDetailMessage}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
        </Grid>
    </Border>
</Window>
