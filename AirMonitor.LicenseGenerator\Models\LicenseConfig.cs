using System;
using System.Collections.Generic;

namespace AirMonitor.LicenseGenerator.Models
{
    /// <summary>
    /// License配置模型
    /// </summary>
    public class LicenseConfig
    {
        /// <summary>
        /// License类型
        /// </summary>
        public string LicenseType { get; set; } = "普通";

        /// <summary>
        /// 设备指纹（手动输入）
        /// </summary>
        public string DeviceFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 有效期类型（永久/指定天数）
        /// </summary>
        public bool IsPermanent { get; set; } = true;

        /// <summary>
        /// 有效天数（当IsPermanent为false时使用）
        /// </summary>
        public int ValidDays { get; set; } = 365;

        /// <summary>
        /// 授权的功能列表
        /// </summary>
        public List<string> AuthorizedFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 授权的界面列表
        /// </summary>
        public List<string> AuthorizedViews { get; set; } = new List<string>();

        /// <summary>
        /// 授权的按钮列表
        /// </summary>
        public List<string> AuthorizedButtons { get; set; } = new List<string>();

        /// <summary>
        /// 自定义属性
        /// </summary>
        public Dictionary<string, string> CustomProperties { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 输出文件路径
        /// </summary>
        public string OutputFilePath { get; set; } = string.Empty;
    }

    /// <summary>
    /// 功能权限项
    /// </summary>
    public class FeatureItem
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsSelected { get; set; } = false;
    }

    /// <summary>
    /// 界面权限项
    /// </summary>
    public class ViewItem
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsSelected { get; set; } = false;
    }

    /// <summary>
    /// 按钮权限项
    /// </summary>
    public class ButtonItem
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string ViewName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsSelected { get; set; } = false;
    }
}
