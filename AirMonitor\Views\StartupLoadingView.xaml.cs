using AirMonitor.ViewModels;
using System.Windows;

namespace AirMonitor.Views
{
    /// <summary>
    /// StartupLoadingView.xaml 的交互逻辑
    /// </summary>
    public partial class StartupLoadingView : Window
    {
        public StartupLoadingView()
        {
            InitializeComponent();
        }

        public StartupLoadingView(StartupLoadingViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }
    }
}
