using AirMonitor.Shared.License;
using AirMonitor.Shared.Constants;
using System;
using System.IO;
using System.Threading.Tasks;

namespace AirMonitor.Services
{
    /// <summary>
    /// 应用程序启动服务
    /// </summary>
    public class StartupService
    {
        private readonly LicenseManager _licenseManager;

        /// <summary>
        /// License检查结果
        /// </summary>
        public class LicenseCheckResult
        {
            public bool IsValid { get; set; }
            public bool FileExists { get; set; }
            public string ErrorCode { get; set; } = string.Empty;
            public string ErrorMessage { get; set; } = string.Empty;
            public string DetailedMessage { get; set; } = string.Empty;
            public string FilePath { get; set; } = string.Empty;
        }

        public StartupService()
        {
            _licenseManager = new LicenseManager();
        }

        /// <summary>
        /// 获取默认License文件路径
        /// </summary>
        /// <returns>默认License文件路径</returns>
        public string GetDefaultLicensePath()
        {
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appDirectory, LicenseConstants.DEFAULT_LICENSE_FILENAME);
        }

        /// <summary>
        /// 执行启动时License检查
        /// </summary>
        /// <returns>License检查结果</returns>
        public async Task<LicenseCheckResult> CheckLicenseOnStartupAsync()
        {
            var result = new LicenseCheckResult();
            var defaultLicensePath = GetDefaultLicensePath();
            result.FilePath = defaultLicensePath;

            try
            {
                // 1. 检查License文件是否存在
                if (!File.Exists(defaultLicensePath))
                {
                    result.FileExists = false;
                    result.ErrorCode = LicenseConstants.ErrorCodes.FILE_NOT_FOUND;
                    result.ErrorMessage = "License文件不存在";
                    result.DetailedMessage = $"在默认路径 {defaultLicensePath} 下未找到License文件。\n\n" +
                                           "请联系管理员获取License文件，或在License验证界面手动选择License文件。";
                    return result;
                }

                result.FileExists = true;

                // 2. 执行完整的License验证
                var validationResult = await _licenseManager.LoadLicenseAsync(defaultLicensePath);

                if (validationResult.IsValid && validationResult.License != null)
                {
                    result.IsValid = true;
                    result.ErrorMessage = "License验证成功";
                    result.DetailedMessage = $"License类型: {validationResult.License.LicenseType}\n" +
                                           $"有效期: {(validationResult.License.ValidTo?.ToString("yyyy-MM-dd") ?? "永久有效")}\n" +
                                           $"授权功能: {validationResult.License.AuthorizedFeatures.Count} 项";
                }
                else
                {
                    result.IsValid = false;
                    result.ErrorCode = validationResult.ErrorCode;
                    result.ErrorMessage = validationResult.ErrorMessage;
                    result.DetailedMessage = GetDetailedErrorMessage(validationResult.ErrorCode, validationResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorCode = LicenseConstants.ErrorCodes.CORRUPTED;
                result.ErrorMessage = "License检查过程中发生错误";
                result.DetailedMessage = $"错误详情: {ex.Message}\n\n" +
                                       "请检查License文件是否完整，或联系技术支持。";
            }

            return result;
        }

        /// <summary>
        /// 获取详细的错误消息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="originalMessage">原始错误消息</param>
        /// <returns>详细错误消息</returns>
        private string GetDetailedErrorMessage(string errorCode, string originalMessage)
        {
            return errorCode switch
            {
                LicenseConstants.ErrorCodes.INVALID_FORMAT => 
                    $"{originalMessage}\n\n" +
                    "License文件格式无效，可能的原因：\n" +
                    "• 文件已损坏\n" +
                    "• 文件不是有效的License文件\n" +
                    "• 文件版本不兼容\n\n" +
                    "请联系管理员重新生成License文件。",

                LicenseConstants.ErrorCodes.DECRYPTION_FAILED => 
                    $"{originalMessage}\n\n" +
                    "License文件解密失败，可能的原因：\n" +
                    "• 文件在传输过程中被损坏\n" +
                    "• 文件不是为此软件版本生成的\n" +
                    "• 文件被非法修改\n\n" +
                    "请联系管理员重新生成License文件。",

                LicenseConstants.ErrorCodes.SIGNATURE_INVALID => 
                    $"{originalMessage}\n\n" +
                    "License文件签名验证失败，可能的原因：\n" +
                    "• 文件被非法修改或篡改\n" +
                    "• 文件来源不可信\n" +
                    "• 文件传输过程中出现错误\n\n" +
                    "为了安全起见，请联系管理员重新生成License文件。",

                LicenseConstants.ErrorCodes.DEVICE_MISMATCH => 
                    $"{originalMessage}\n\n" +
                    "设备指纹不匹配，可能的原因：\n" +
                    "• License文件是为其他设备生成的\n" +
                    "• 设备硬件发生了变更\n" +
                    "• 设备信息获取异常\n\n" +
                    "请在License验证界面获取当前设备指纹，并联系管理员生成新的License文件。",

                LicenseConstants.ErrorCodes.EXPIRED => 
                    $"{originalMessage}\n\n" +
                    "License已过期，需要续期：\n" +
                    "• 联系管理员获取新的License文件\n" +
                    "• 或申请License续期\n\n" +
                    "在获得新License之前，软件将无法正常使用。",

                LicenseConstants.ErrorCodes.CORRUPTED => 
                    $"{originalMessage}\n\n" +
                    "License文件已损坏，可能的原因：\n" +
                    "• 文件存储介质出现问题\n" +
                    "• 文件传输过程中出现错误\n" +
                    "• 病毒或恶意软件影响\n\n" +
                    "请联系管理员重新获取License文件。",

                _ => $"{originalMessage}\n\n请联系技术支持获取帮助。"
            };
        }

        /// <summary>
        /// 获取License管理器实例
        /// </summary>
        /// <returns>License管理器</returns>
        public LicenseManager GetLicenseManager()
        {
            return _licenseManager;
        }
    }
}
