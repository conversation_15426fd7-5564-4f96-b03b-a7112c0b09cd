using AirMonitor.Models;
using System;
using System.IO;
using System.Threading.Tasks;

namespace AirMonitor.Services
{
    /// <summary>
    /// License验证服务
    /// </summary>
    public class LicenseService
    {
        /// <summary>
        /// 验证License文件
        /// </summary>
        /// <param name="filePath">License文件路径</param>
        /// <returns>License信息</returns>
        public async Task<LicenseInfo> ValidateLicenseAsync(string filePath)
        {
            var licenseInfo = new LicenseInfo
            {
                FilePath = filePath
            };

            try
            {
                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    licenseInfo.StatusMessage = "License文件不存在";
                    return licenseInfo;
                }

                // TODO: 实现License文件解密和验证逻辑
                // 这里暂时使用模拟验证，后续需要集成AirMonitor.Shared中的验证逻辑
                await Task.Delay(1000); // 模拟验证过程

                // 模拟验证结果
                licenseInfo.IsValidated = true;
                licenseInfo.StatusMessage = "License验证成功";
                licenseInfo.ExpiryDate = DateTime.Now.AddDays(30);
                licenseInfo.AuthorizedFeatures.AddRange(new[] { "基础功能", "高级功能" });
                licenseInfo.DeviceFingerprint = "DEMO-DEVICE-FINGERPRINT";
            }
            catch (Exception ex)
            {
                licenseInfo.StatusMessage = $"License验证失败: {ex.Message}";
            }

            return licenseInfo;
        }

        /// <summary>
        /// 检查功能是否被授权
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <param name="licenseInfo">License信息</param>
        /// <returns>是否被授权</returns>
        public bool IsFeatureAuthorized(string featureName, LicenseInfo licenseInfo)
        {
            if (!licenseInfo.IsValid)
                return false;

            return licenseInfo.AuthorizedFeatures.Contains(featureName);
        }

        /// <summary>
        /// 获取默认License文件路径
        /// </summary>
        /// <returns>默认License文件路径</returns>
        public string GetDefaultLicensePath()
        {
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appDirectory, "license.lic");
        }
    }
}
