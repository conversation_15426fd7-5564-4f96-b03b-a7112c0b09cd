using AirMonitor.Models;
using AirMonitor.Shared.License;
using AirMonitor.Shared.Constants;
using System;
using System.IO;
using System.Threading.Tasks;

namespace AirMonitor.Services
{
    /// <summary>
    /// License验证服务
    /// </summary>
    public class LicenseService
    {
        private readonly LicenseManager _licenseManager;

        public LicenseService()
        {
            _licenseManager = new LicenseManager();
        }

        /// <summary>
        /// 验证License文件
        /// </summary>
        /// <param name="filePath">License文件路径</param>
        /// <returns>License信息</returns>
        public async Task<LicenseInfo> ValidateLicenseAsync(string filePath)
        {
            var licenseInfo = new LicenseInfo
            {
                FilePath = filePath
            };

            try
            {
                // 使用共享的License验证逻辑
                var validationResult = await _licenseManager.LoadLicenseAsync(filePath);

                if (validationResult.IsValid && validationResult.License != null)
                {
                    var license = validationResult.License;

                    licenseInfo.IsValidated = true;
                    licenseInfo.StatusMessage = "License验证成功";
                    licenseInfo.ExpiryDate = license.ValidTo;
                    licenseInfo.DeviceFingerprint = license.DeviceFingerprint;

                    // 转换授权功能列表
                    licenseInfo.AuthorizedFeatures.AddRange(license.AuthorizedFeatures);
                }
                else
                {
                    licenseInfo.IsValidated = false;
                    licenseInfo.StatusMessage = GetErrorMessage(validationResult.ErrorCode, validationResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                licenseInfo.StatusMessage = $"License验证失败: {ex.Message}";
            }

            return licenseInfo;
        }

        /// <summary>
        /// 检查功能是否被授权
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <returns>是否被授权</returns>
        public bool IsFeatureAuthorized(string featureName)
        {
            return _licenseManager.IsFeatureAuthorized(featureName);
        }

        /// <summary>
        /// 检查界面是否被授权
        /// </summary>
        /// <param name="viewName">界面名称</param>
        /// <returns>是否被授权</returns>
        public bool IsViewAuthorized(string viewName)
        {
            return _licenseManager.IsViewAuthorized(viewName);
        }

        /// <summary>
        /// 检查按钮是否被授权
        /// </summary>
        /// <param name="buttonName">按钮名称</param>
        /// <returns>是否被授权</returns>
        public bool IsButtonAuthorized(string buttonName)
        {
            return _licenseManager.IsButtonAuthorized(buttonName);
        }

        /// <summary>
        /// 获取默认License文件路径
        /// </summary>
        /// <returns>默认License文件路径</returns>
        public string GetDefaultLicensePath()
        {
            var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            return Path.Combine(appDirectory, LicenseConstants.DEFAULT_LICENSE_FILENAME);
        }

        /// <summary>
        /// 获取License管理器实例
        /// </summary>
        /// <returns>License管理器</returns>
        public LicenseManager GetLicenseManager()
        {
            return _licenseManager;
        }

        /// <summary>
        /// 将错误代码转换为用户友好的错误消息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="originalMessage">原始错误消息</param>
        /// <returns>用户友好的错误消息</returns>
        private string GetErrorMessage(string errorCode, string originalMessage)
        {
            return errorCode switch
            {
                LicenseConstants.ErrorCodes.FILE_NOT_FOUND => "License文件不存在，请检查文件路径",
                LicenseConstants.ErrorCodes.INVALID_FORMAT => "License文件格式无效，请使用正确的License文件",
                LicenseConstants.ErrorCodes.DECRYPTION_FAILED => "License文件解密失败，文件可能已损坏",
                LicenseConstants.ErrorCodes.SIGNATURE_INVALID => "License文件签名验证失败，文件可能被篡改",
                LicenseConstants.ErrorCodes.DEVICE_MISMATCH => "License与当前设备不匹配，请使用正确的License文件",
                LicenseConstants.ErrorCodes.EXPIRED => "License已过期，请联系供应商获取新的License",
                LicenseConstants.ErrorCodes.CORRUPTED => "License文件已损坏，请重新获取License文件",
                _ => originalMessage
            };
        }
    }
}
