using AirMonitor.LicenseGenerator.ViewModels;
using System.Windows;

namespace AirMonitor.LicenseGenerator.Views
{
    /// <summary>
    /// LicenseGeneratorView.xaml 的交互逻辑
    /// </summary>
    public partial class LicenseGeneratorView : Window
    {
        public LicenseGeneratorView()
        {
            InitializeComponent();
        }

        public LicenseGeneratorView(LicenseGeneratorViewModel viewModel) : this()
        {
            DataContext = viewModel;
        }
    }
}
