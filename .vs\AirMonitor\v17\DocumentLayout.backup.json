{"Version": 1, "WorkspaceRootPath": "D:\\Project\\02 AirMonitor\\AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2C926FF8-8C72-497D-8C2D-D98AE32960CD}|AirMonitor\\AirMonitor.csproj|d:\\project\\02 airmonitor\\airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{2C926FF8-8C72-497D-8C2D-D98AE32960CD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{2C926FF8-8C72-497D-8C2D-D98AE32960CD}|AirMonitor\\AirMonitor.csproj|d:\\project\\02 airmonitor\\airmonitor\\airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2C926FF8-8C72-497D-8C2D-D98AE32960CD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\Project\\02 AirMonitor\\AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\Project\\02 AirMonitor\\AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-07T07:15:36.11Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\Project\\02 AirMonitor\\AirMonitor\\AirMonitor\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml.cs", "ToolTip": "D:\\Project\\02 AirMonitor\\AirMonitor\\AirMonitor\\MainWindow.xaml.cs", "RelativeToolTip": "AirMonitor\\MainWindow.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T07:15:34.006Z", "EditorCaption": ""}]}]}]}