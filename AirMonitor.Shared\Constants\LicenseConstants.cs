namespace AirMonitor.Shared.Constants
{
    /// <summary>
    /// License相关常量定义
    /// </summary>
    public static class LicenseConstants
    {
        /// <summary>
        /// License文件扩展名
        /// </summary>
        public const string LICENSE_FILE_EXTENSION = ".lic";

        /// <summary>
        /// License文件默认名称
        /// </summary>
        public const string DEFAULT_LICENSE_FILENAME = "license.lic";

        /// <summary>
        /// License加密密钥长度
        /// </summary>
        public const int ENCRYPTION_KEY_LENGTH = 32;

        /// <summary>
        /// License加密向量长度
        /// </summary>
        public const int ENCRYPTION_IV_LENGTH = 16;

        /// <summary>
        /// License版本号
        /// </summary>
        public const string LICENSE_VERSION = "1.0";

        /// <summary>
        /// License签名算法
        /// </summary>
        public const string SIGNATURE_ALGORITHM = "SHA256";

        /// <summary>
        /// 设备指纹分隔符
        /// </summary>
        public const string DEVICE_FINGERPRINT_SEPARATOR = "|";

        /// <summary>
        /// License验证错误代码
        /// </summary>
        public static class ErrorCodes
        {
            public const string FILE_NOT_FOUND = "LICENSE_001";
            public const string INVALID_FORMAT = "LICENSE_002";
            public const string DECRYPTION_FAILED = "LICENSE_003";
            public const string SIGNATURE_INVALID = "LICENSE_004";
            public const string DEVICE_MISMATCH = "LICENSE_005";
            public const string EXPIRED = "LICENSE_006";
            public const string CORRUPTED = "LICENSE_007";
        }

        /// <summary>
        /// 预定义的功能权限
        /// </summary>
        public static class Features
        {
            public const string BASIC_MONITORING = "BASIC_MONITORING";
            public const string ADVANCED_MONITORING = "ADVANCED_MONITORING";
            public const string DATA_EXPORT = "DATA_EXPORT";
            public const string REPORT_GENERATION = "REPORT_GENERATION";
            public const string SYSTEM_SETTINGS = "SYSTEM_SETTINGS";
            public const string USER_MANAGEMENT = "USER_MANAGEMENT";
            public const string DEVICE_CONTROL = "DEVICE_CONTROL";
            public const string ALARM_MANAGEMENT = "ALARM_MANAGEMENT";
        }

        /// <summary>
        /// License类型
        /// </summary>
        public static class LicenseTypes
        {
            public const string NORMAL = "普通";
            public const string AFTER_SALES = "售后";
            public const string DEVELOPMENT = "研发";
            public const string MANAGEMENT = "管理";
        }
    }
}
