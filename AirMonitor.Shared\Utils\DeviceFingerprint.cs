using AirMonitor.Shared.Models;
using System;
using System.Linq;
using System.Management;
using System.Net.NetworkInformation;
using System.Threading.Tasks;

namespace AirMonitor.Shared.Utils
{
    /// <summary>
    /// 设备指纹生成工具
    /// </summary>
    public static class DeviceFingerprint
    {
        /// <summary>
        /// 获取当前设备信息
        /// </summary>
        /// <returns>设备信息</returns>
        public static async Task<DeviceInfo> GetDeviceInfoAsync()
        {
            var deviceInfo = new DeviceInfo();

            try
            {
                // 并行获取各种硬件信息以提高性能
                var tasks = new[]
                {
                    Task.Run(() => deviceInfo.MotherboardSerial = GetMotherboardSerial()),
                    Task.Run(() => deviceInfo.CpuSerial = GetCpuSerial()),
                    Task.Run(() => deviceInfo.HardDiskSerial = GetHardDiskSerial()),
                    Task.Run(() => deviceInfo.MacAddress = GetMacAddress()),
                    Task.Run(() => deviceInfo.ComputerName = Environment.MachineName),
                    Task.Run(() => deviceInfo.OSVersion = Environment.OSVersion.ToString())
                };

                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，使用备用方案
                Console.WriteLine($"获取设备信息时发生错误: {ex.Message}");
                
                // 使用基本信息作为备用方案
                deviceInfo.ComputerName = Environment.MachineName;
                deviceInfo.OSVersion = Environment.OSVersion.ToString();
                deviceInfo.MotherboardSerial = "UNKNOWN";
                deviceInfo.CpuSerial = "UNKNOWN";
                deviceInfo.HardDiskSerial = "UNKNOWN";
                deviceInfo.MacAddress = "UNKNOWN";
            }

            return deviceInfo;
        }

        /// <summary>
        /// 生成当前设备的指纹
        /// </summary>
        /// <returns>设备指纹</returns>
        public static async Task<string> GenerateCurrentDeviceFingerprintAsync()
        {
            var deviceInfo = await GetDeviceInfoAsync();
            return deviceInfo.GenerateFingerprint();
        }

        /// <summary>
        /// 验证设备指纹是否匹配当前设备
        /// </summary>
        /// <param name="fingerprint">要验证的指纹</param>
        /// <returns>是否匹配</returns>
        public static async Task<bool> ValidateDeviceFingerprintAsync(string fingerprint)
        {
            var currentFingerprint = await GenerateCurrentDeviceFingerprintAsync();
            return currentFingerprint.Equals(fingerprint, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 获取主板序列号
        /// </summary>
        /// <returns>主板序列号</returns>
        private static string GetMotherboardSerial()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard");
                using var collection = searcher.Get();
                
                foreach (ManagementObject obj in collection)
                {
                    var serial = obj["SerialNumber"]?.ToString();
                    if (!string.IsNullOrWhiteSpace(serial))
                        return serial.Trim();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取主板序列号失败: {ex.Message}");
            }
            
            return "UNKNOWN_MOTHERBOARD";
        }

        /// <summary>
        /// 获取CPU序列号
        /// </summary>
        /// <returns>CPU序列号</returns>
        private static string GetCpuSerial()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
                using var collection = searcher.Get();
                
                foreach (ManagementObject obj in collection)
                {
                    var serial = obj["ProcessorId"]?.ToString();
                    if (!string.IsNullOrWhiteSpace(serial))
                        return serial.Trim();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取CPU序列号失败: {ex.Message}");
            }
            
            return "UNKNOWN_CPU";
        }

        /// <summary>
        /// 获取硬盘序列号
        /// </summary>
        /// <returns>硬盘序列号</returns>
        private static string GetHardDiskSerial()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
                using var collection = searcher.Get();
                
                foreach (ManagementObject obj in collection)
                {
                    var serial = obj["SerialNumber"]?.ToString();
                    if (!string.IsNullOrWhiteSpace(serial))
                        return serial.Trim();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取硬盘序列号失败: {ex.Message}");
            }
            
            return "UNKNOWN_HARDDISK";
        }

        /// <summary>
        /// 获取MAC地址
        /// </summary>
        /// <returns>MAC地址</returns>
        private static string GetMacAddress()
        {
            try
            {
                var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                    .Where(ni => ni.OperationalStatus == OperationalStatus.Up && 
                                ni.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    .ToArray();

                foreach (var ni in networkInterfaces)
                {
                    var mac = ni.GetPhysicalAddress().ToString();
                    if (!string.IsNullOrWhiteSpace(mac) && mac != "000000000000")
                        return mac;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取MAC地址失败: {ex.Message}");
            }
            
            return "UNKNOWN_MAC";
        }
    }
}
