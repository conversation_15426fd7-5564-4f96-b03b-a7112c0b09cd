using CommunityToolkit.Mvvm.ComponentModel;
using System;

namespace AirMonitor.ViewModels
{
    /// <summary>
    /// 启动加载界面ViewModel
    /// </summary>
    public partial class StartupLoadingViewModel : ObservableObject
    {
        [ObservableProperty]
        private string _statusMessage = "正在启动 AirMonitor...";

        [ObservableProperty]
        private string _detailMessage = string.Empty;

        [ObservableProperty]
        private bool _isLoading = true;

        [ObservableProperty]
        private bool _showDetailMessage = false;

        [ObservableProperty]
        private double _progressValue = 0;

        /// <summary>
        /// 更新状态信息
        /// </summary>
        /// <param name="message">状态消息</param>
        /// <param name="detail">详细信息</param>
        /// <param name="progress">进度值（0-100）</param>
        public void UpdateStatus(string message, string detail = "", double progress = -1)
        {
            StatusMessage = message;
            DetailMessage = detail;
            ShowDetailMessage = !string.IsNullOrEmpty(detail);
            
            if (progress >= 0)
            {
                IsLoading = false;
                ProgressValue = progress;
            }
            else
            {
                IsLoading = true;
            }
        }

        /// <summary>
        /// 设置完成状态
        /// </summary>
        /// <param name="message">完成消息</param>
        public void SetCompleted(string message = "启动完成")
        {
            StatusMessage = message;
            IsLoading = false;
            ProgressValue = 100;
            ShowDetailMessage = false;
        }

        /// <summary>
        /// 设置错误状态
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="detail">错误详情</param>
        public void SetError(string message, string detail = "")
        {
            StatusMessage = message;
            DetailMessage = detail;
            ShowDetailMessage = !string.IsNullOrEmpty(detail);
            IsLoading = false;
            ProgressValue = 0;
        }
    }
}
