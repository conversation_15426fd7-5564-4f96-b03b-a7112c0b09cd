using AirMonitor.Models;
using AirMonitor.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace AirMonitor.ViewModels
{
    /// <summary>
    /// License验证界面ViewModel
    /// </summary>
    public partial class LicenseViewModel : ObservableObject
    {
        private readonly LicenseService _licenseService;

        /// <summary>
        /// License验证成功事件
        /// </summary>
        public event EventHandler? LicenseValidated;

        [ObservableProperty]
        private string _licenseFilePath = string.Empty;

        [ObservableProperty]
        private string _statusMessage = "请选择License文件进行验证";

        [ObservableProperty]
        private bool _isValidating = false;

        [ObservableProperty]
        private bool _isLicenseValid = false;

        [ObservableProperty]
        private string _expiryDateText = string.Empty;

        [ObservableProperty]
        private string _authorizedFeaturesText = string.Empty;

        [ObservableProperty]
        private bool _canProceed = false;

        [ObservableProperty]
        private bool _isGettingDeviceFingerprint = false;

        [ObservableProperty]
        private bool _isDeviceFingerprintLoaded = false;

        [ObservableProperty]
        private string _currentDeviceFingerprint = string.Empty;

        [ObservableProperty]
        private string _deviceInfoText = string.Empty;

        public LicenseInfo? CurrentLicenseInfo { get; private set; }

        public LicenseViewModel(LicenseService licenseService)
        {
            _licenseService = licenseService;
            
            // 尝试加载默认License文件
            LicenseFilePath = _licenseService.GetDefaultLicensePath();
        }

        /// <summary>
        /// 选择License文件命令
        /// </summary>
        [RelayCommand]
        private void SelectLicenseFile()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择License文件",
                Filter = "License文件 (*.lic)|*.lic|所有文件 (*.*)|*.*",
                DefaultExt = "lic"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LicenseFilePath = openFileDialog.FileName;
            }
        }

        /// <summary>
        /// 验证License命令
        /// </summary>
        [RelayCommand]
        private async Task ValidateLicenseAsync()
        {
            if (string.IsNullOrWhiteSpace(LicenseFilePath))
            {
                StatusMessage = "请先选择License文件";
                return;
            }

            IsValidating = true;
            CanProceed = false;

            try
            {
                StatusMessage = "正在验证License...";
                CurrentLicenseInfo = await _licenseService.ValidateLicenseAsync(LicenseFilePath);

                IsLicenseValid = CurrentLicenseInfo.IsValid;
                StatusMessage = CurrentLicenseInfo.StatusMessage;

                if (IsLicenseValid)
                {
                    ExpiryDateText = CurrentLicenseInfo.ExpiryDate?.ToString("yyyy-MM-dd") ?? "永久有效";
                    AuthorizedFeaturesText = string.Join(", ", CurrentLicenseInfo.AuthorizedFeatures);
                    CanProceed = true;
                }
                else
                {
                    ExpiryDateText = string.Empty;
                    AuthorizedFeaturesText = string.Empty;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"验证过程中发生错误: {ex.Message}";
                IsLicenseValid = false;
                CanProceed = false;
            }
            finally
            {
                IsValidating = false;
            }
        }

        /// <summary>
        /// 继续使用软件命令
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanProceed))]
        private void ProceedToApplication()
        {
            // 触发License验证成功事件
            LicenseValidated?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 获取当前设备指纹命令
        /// </summary>
        [RelayCommand]
        private async Task GetCurrentDeviceFingerprintAsync()
        {
            if (IsGettingDeviceFingerprint) return;

            try
            {
                IsGettingDeviceFingerprint = true;
                StatusMessage = "正在获取设备指纹...";

                // 使用共享库获取设备信息
                var deviceInfo = await AirMonitor.Shared.Utils.DeviceFingerprint.GetDeviceInfoAsync();
                CurrentDeviceFingerprint = deviceInfo.GenerateFingerprint();

                // 显示设备详细信息
                DeviceInfoText = $"计算机名: {deviceInfo.ComputerName}\n" +
                               $"操作系统: {deviceInfo.OSVersion}\n" +
                               $"主板序列号: {deviceInfo.MotherboardSerial}\n" +
                               $"CPU序列号: {deviceInfo.CpuSerial}\n" +
                               $"硬盘序列号: {deviceInfo.HardDiskSerial}\n" +
                               $"MAC地址: {deviceInfo.MacAddress}\n" +
                               $"设备指纹: {CurrentDeviceFingerprint}";

                IsDeviceFingerprintLoaded = true;
                StatusMessage = "设备指纹获取成功";
            }
            catch (Exception ex)
            {
                StatusMessage = $"获取设备指纹失败: {ex.Message}";
                MessageBox.Show($"获取设备指纹失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsGettingDeviceFingerprint = false;
            }
        }

        /// <summary>
        /// 复制设备指纹到剪贴板命令
        /// </summary>
        [RelayCommand]
        private void CopyDeviceFingerprintToClipboard()
        {
            try
            {
                if (!string.IsNullOrEmpty(CurrentDeviceFingerprint))
                {
                    Clipboard.SetText(CurrentDeviceFingerprint);
                    StatusMessage = "设备指纹已复制到剪贴板";
                    MessageBox.Show("设备指纹已复制到剪贴板", "复制成功",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("请先获取设备指纹", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"复制失败: {ex.Message}";
                MessageBox.Show($"复制失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 退出应用程序命令
        /// </summary>
        [RelayCommand]
        private void ExitApplication()
        {
            Application.Current.Shutdown();
        }
    }
}
