using AirMonitor.Models;
using AirMonitor.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace AirMonitor.ViewModels
{
    /// <summary>
    /// License验证界面ViewModel
    /// </summary>
    public partial class LicenseViewModel : ObservableObject
    {
        private readonly LicenseService _licenseService;

        [ObservableProperty]
        private string _licenseFilePath = string.Empty;

        [ObservableProperty]
        private string _statusMessage = "请选择License文件进行验证";

        [ObservableProperty]
        private bool _isValidating = false;

        [ObservableProperty]
        private bool _isLicenseValid = false;

        [ObservableProperty]
        private string _expiryDateText = string.Empty;

        [ObservableProperty]
        private string _authorizedFeaturesText = string.Empty;

        [ObservableProperty]
        private bool _canProceed = false;

        public LicenseInfo? CurrentLicenseInfo { get; private set; }

        public LicenseViewModel(LicenseService licenseService)
        {
            _licenseService = licenseService;
            
            // 尝试加载默认License文件
            LicenseFilePath = _licenseService.GetDefaultLicensePath();
        }

        /// <summary>
        /// 选择License文件命令
        /// </summary>
        [RelayCommand]
        private void SelectLicenseFile()
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择License文件",
                Filter = "License文件 (*.lic)|*.lic|所有文件 (*.*)|*.*",
                DefaultExt = "lic"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LicenseFilePath = openFileDialog.FileName;
            }
        }

        /// <summary>
        /// 验证License命令
        /// </summary>
        [RelayCommand]
        private async Task ValidateLicenseAsync()
        {
            if (string.IsNullOrWhiteSpace(LicenseFilePath))
            {
                StatusMessage = "请先选择License文件";
                return;
            }

            IsValidating = true;
            CanProceed = false;

            try
            {
                StatusMessage = "正在验证License...";
                CurrentLicenseInfo = await _licenseService.ValidateLicenseAsync(LicenseFilePath);

                IsLicenseValid = CurrentLicenseInfo.IsValid;
                StatusMessage = CurrentLicenseInfo.StatusMessage;

                if (IsLicenseValid)
                {
                    ExpiryDateText = CurrentLicenseInfo.ExpiryDate?.ToString("yyyy-MM-dd") ?? "永久有效";
                    AuthorizedFeaturesText = string.Join(", ", CurrentLicenseInfo.AuthorizedFeatures);
                    CanProceed = true;
                }
                else
                {
                    ExpiryDateText = string.Empty;
                    AuthorizedFeaturesText = string.Empty;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"验证过程中发生错误: {ex.Message}";
                IsLicenseValid = false;
                CanProceed = false;
            }
            finally
            {
                IsValidating = false;
            }
        }

        /// <summary>
        /// 继续使用软件命令
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanProceed))]
        private void ProceedToApplication()
        {
            // TODO: 关闭License验证窗口，打开主应用程序窗口
            // 这里需要与主窗口进行交互
            MessageBox.Show("License验证成功，即将进入主程序", "验证成功", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 退出应用程序命令
        /// </summary>
        [RelayCommand]
        private void ExitApplication()
        {
            Application.Current.Shutdown();
        }
    }
}
