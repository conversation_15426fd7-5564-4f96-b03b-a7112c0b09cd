using AirMonitor.LicenseGenerator.Models;
using AirMonitor.Shared.Constants;
using AirMonitor.Shared.License;
using AirMonitor.Shared.Models;
using AirMonitor.Shared.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace AirMonitor.LicenseGenerator.Services
{
    /// <summary>
    /// License生成服务
    /// </summary>
    public class LicenseGeneratorService
    {
        private readonly LicenseManager _licenseManager;

        public LicenseGeneratorService()
        {
            _licenseManager = new LicenseManager();
        }

        /// <summary>
        /// 验证设备指纹格式
        /// </summary>
        /// <param name="fingerprint">设备指纹字符串</param>
        /// <returns>是否为有效格式</returns>
        public bool ValidateDeviceFingerprintFormat(string fingerprint)
        {
            // 检查设备指纹格式：应该是Base64编码的字符串，长度在20-100字符之间
            if (string.IsNullOrWhiteSpace(fingerprint))
                return false;

            if (fingerprint.Length < 20 || fingerprint.Length > 100)
                return false;

            // 检查是否为有效的Base64字符串
            try
            {
                Convert.FromBase64String(fingerprint);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取可用的功能权限列表
        /// </summary>
        /// <returns>功能权限列表</returns>
        public List<FeatureItem> GetAvailableFeatures()
        {
            return new List<FeatureItem>
            {
                new FeatureItem
                {
                    Name = LicenseConstants.Features.BASIC_MONITORING,
                    DisplayName = "基础监控",
                    Description = "基本的设备监控功能",
                    IsSelected = true
                },
                new FeatureItem
                {
                    Name = LicenseConstants.Features.ADVANCED_MONITORING,
                    DisplayName = "高级监控",
                    Description = "高级监控和分析功能"
                },
                new FeatureItem
                {
                    Name = LicenseConstants.Features.DATA_EXPORT,
                    DisplayName = "数据导出",
                    Description = "数据导出和备份功能",
                    IsSelected = true
                },
                new FeatureItem
                {
                    Name = LicenseConstants.Features.REPORT_GENERATION,
                    DisplayName = "报表生成",
                    Description = "自动生成各类报表"
                },
                new FeatureItem
                {
                    Name = LicenseConstants.Features.SYSTEM_SETTINGS,
                    DisplayName = "系统设置",
                    Description = "系统配置和参数设置",
                    IsSelected = true
                },
                new FeatureItem
                {
                    Name = LicenseConstants.Features.USER_MANAGEMENT,
                    DisplayName = "用户管理",
                    Description = "用户账户和权限管理"
                },
                new FeatureItem
                {
                    Name = LicenseConstants.Features.DEVICE_CONTROL,
                    DisplayName = "设备控制",
                    Description = "远程设备控制功能"
                },
                new FeatureItem
                {
                    Name = LicenseConstants.Features.ALARM_MANAGEMENT,
                    DisplayName = "报警管理",
                    Description = "报警设置和管理功能"
                }
            };
        }

        /// <summary>
        /// 获取可用的界面权限列表
        /// </summary>
        /// <returns>界面权限列表</returns>
        public List<ViewItem> GetAvailableViews()
        {
            return new List<ViewItem>
            {
                new ViewItem
                {
                    Name = "MainView",
                    DisplayName = "主界面",
                    Description = "应用程序主界面",
                    IsSelected = true
                },
                new ViewItem
                {
                    Name = "MonitoringView",
                    DisplayName = "监控界面",
                    Description = "设备监控界面"
                },
                new ViewItem
                {
                    Name = "SettingsView",
                    DisplayName = "设置界面",
                    Description = "系统设置界面",
                    IsSelected = true
                },
                new ViewItem
                {
                    Name = "ReportsView",
                    DisplayName = "报表界面",
                    Description = "报表查看和生成界面"
                },
                new ViewItem
                {
                    Name = "DeviceControlView",
                    DisplayName = "设备控制界面",
                    Description = "设备远程控制界面"
                },
                new ViewItem
                {
                    Name = "AlarmView",
                    DisplayName = "报警界面",
                    Description = "报警信息查看界面"
                }
            };
        }

        /// <summary>
        /// 获取可用的按钮权限列表
        /// </summary>
        /// <returns>按钮权限列表</returns>
        public List<ButtonItem> GetAvailableButtons()
        {
            return new List<ButtonItem>
            {
                new ButtonItem
                {
                    Name = "StartMonitoring",
                    DisplayName = "开始监控",
                    ViewName = "MonitoringView",
                    Description = "启动设备监控",
                    IsSelected = true
                },
                new ButtonItem
                {
                    Name = "StopMonitoring",
                    DisplayName = "停止监控",
                    ViewName = "MonitoringView",
                    Description = "停止设备监控",
                    IsSelected = true
                },
                new ButtonItem
                {
                    Name = "ExportData",
                    DisplayName = "导出数据",
                    ViewName = "MainView",
                    Description = "导出监控数据"
                },
                new ButtonItem
                {
                    Name = "GenerateReport",
                    DisplayName = "生成报表",
                    ViewName = "ReportsView",
                    Description = "生成分析报表"
                },
                new ButtonItem
                {
                    Name = "DeviceControl",
                    DisplayName = "设备控制",
                    ViewName = "DeviceControlView",
                    Description = "远程控制设备"
                },
                new ButtonItem
                {
                    Name = "SystemSettings",
                    DisplayName = "系统设置",
                    ViewName = "SettingsView",
                    Description = "修改系统设置",
                    IsSelected = true
                },
                new ButtonItem
                {
                    Name = "AlarmSettings",
                    DisplayName = "报警设置",
                    ViewName = "AlarmView",
                    Description = "配置报警规则"
                }
            };
        }

        /// <summary>
        /// 生成License文件
        /// </summary>
        /// <param name="config">License配置</param>
        /// <returns>生成结果</returns>
        public async Task<(bool Success, string Message)> GenerateLicenseAsync(LicenseConfig config)
        {
            try
            {
                // 验证配置
                var validationResult = ValidateConfig(config);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.Message);
                }

                // 创建License模型
                var license = new LicenseModel
                {
                    LicenseType = config.LicenseType,
                    CustomerName = "AirMonitor用户", // 固定客户名称
                    CustomerEmail = string.Empty, // 不使用邮箱
                    DeviceFingerprint = config.DeviceFingerprint,
                    ValidFrom = DateTime.Now,
                    ValidTo = config.IsPermanent ? null : DateTime.Now.AddDays(config.ValidDays),
                    MaxConcurrentUsers = 1 // 固定为1，一机一License
                };

                // 添加授权功能
                license.AuthorizedFeatures.AddRange(config.AuthorizedFeatures);
                license.AuthorizedViews.AddRange(config.AuthorizedViews);
                license.AuthorizedButtons.AddRange(config.AuthorizedButtons);

                // 添加自定义属性
                foreach (var prop in config.CustomProperties)
                {
                    license.CustomProperties[prop.Key] = prop.Value;
                }

                // 生成License文件
                var success = await _licenseManager.GenerateLicenseFileAsync(license, config.OutputFilePath);
                
                if (success)
                {
                    return (true, $"License文件生成成功：{config.OutputFilePath}");
                }
                else
                {
                    return (false, "License文件生成失败");
                }
            }
            catch (Exception ex)
            {
                return (false, $"生成License时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 验证License配置
        /// </summary>
        /// <param name="config">License配置</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string Message) ValidateConfig(LicenseConfig config)
        {
            if (string.IsNullOrWhiteSpace(config.DeviceFingerprint))
                return (false, "设备指纹不能为空");

            if (!ValidateDeviceFingerprintFormat(config.DeviceFingerprint))
                return (false, "设备指纹格式无效，请输入有效的设备指纹字符串");

            if (string.IsNullOrWhiteSpace(config.OutputFilePath))
                return (false, "输出文件路径不能为空");

            if (!config.IsPermanent && config.ValidDays <= 0)
                return (false, "有效天数必须大于0");

            // 检查输出目录是否存在
            var directory = Path.GetDirectoryName(config.OutputFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                try
                {
                    Directory.CreateDirectory(directory);
                }
                catch (Exception ex)
                {
                    return (false, $"无法创建输出目录：{ex.Message}");
                }
            }

            return (true, string.Empty);
        }
    }
}
