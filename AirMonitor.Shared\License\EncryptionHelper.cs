using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace AirMonitor.Shared.License
{
    /// <summary>
    /// 加密解密工具类
    /// </summary>
    public static class EncryptionHelper
    {
        // 固定的加密密钥和向量（实际项目中应该使用更安全的密钥管理方式）
        private static readonly byte[] DefaultKey = Encoding.UTF8.GetBytes("AirMonitor2024SecretKey123456789");
        private static readonly byte[] DefaultIV = Encoding.UTF8.GetBytes("AirMonitor2024IV");

        /// <summary>
        /// 使用AES加密字符串
        /// </summary>
        /// <param name="plainText">要加密的明文</param>
        /// <param name="key">加密密钥（可选，使用默认密钥）</param>
        /// <param name="iv">初始化向量（可选，使用默认IV）</param>
        /// <returns>加密后的Base64字符串</returns>
        public static string EncryptString(string plainText, byte[]? key = null, byte[]? iv = null)
        {
            if (string.IsNullOrEmpty(plainText))
                throw new ArgumentException("明文不能为空", nameof(plainText));

            key ??= DefaultKey;
            iv ??= DefaultIV;

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);
            
            swEncrypt.Write(plainText);
            swEncrypt.Close();
            
            return Convert.ToBase64String(msEncrypt.ToArray());
        }

        /// <summary>
        /// 使用AES解密字符串
        /// </summary>
        /// <param name="cipherText">要解密的Base64密文</param>
        /// <param name="key">解密密钥（可选，使用默认密钥）</param>
        /// <param name="iv">初始化向量（可选，使用默认IV）</param>
        /// <returns>解密后的明文</returns>
        public static string DecryptString(string cipherText, byte[]? key = null, byte[]? iv = null)
        {
            if (string.IsNullOrEmpty(cipherText))
                throw new ArgumentException("密文不能为空", nameof(cipherText));

            key ??= DefaultKey;
            iv ??= DefaultIV;

            try
            {
                var cipherBytes = Convert.FromBase64String(cipherText);

                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(cipherBytes);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);
                
                return srDecrypt.ReadToEnd();
            }
            catch (Exception ex)
            {
                throw new CryptographicException("解密失败", ex);
            }
        }

        /// <summary>
        /// 加密文件
        /// </summary>
        /// <param name="inputFilePath">输入文件路径</param>
        /// <param name="outputFilePath">输出文件路径</param>
        /// <param name="key">加密密钥（可选）</param>
        /// <param name="iv">初始化向量（可选）</param>
        public static void EncryptFile(string inputFilePath, string outputFilePath, byte[]? key = null, byte[]? iv = null)
        {
            if (!File.Exists(inputFilePath))
                throw new FileNotFoundException("输入文件不存在", inputFilePath);

            key ??= DefaultKey;
            iv ??= DefaultIV;

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var encryptor = aes.CreateEncryptor();
            using var inputStream = File.OpenRead(inputFilePath);
            using var outputStream = File.Create(outputFilePath);
            using var cryptoStream = new CryptoStream(outputStream, encryptor, CryptoStreamMode.Write);
            
            inputStream.CopyTo(cryptoStream);
        }

        /// <summary>
        /// 解密文件
        /// </summary>
        /// <param name="inputFilePath">输入文件路径</param>
        /// <param name="outputFilePath">输出文件路径</param>
        /// <param name="key">解密密钥（可选）</param>
        /// <param name="iv">初始化向量（可选）</param>
        public static void DecryptFile(string inputFilePath, string outputFilePath, byte[]? key = null, byte[]? iv = null)
        {
            if (!File.Exists(inputFilePath))
                throw new FileNotFoundException("输入文件不存在", inputFilePath);

            key ??= DefaultKey;
            iv ??= DefaultIV;

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var decryptor = aes.CreateDecryptor();
            using var inputStream = File.OpenRead(inputFilePath);
            using var cryptoStream = new CryptoStream(inputStream, decryptor, CryptoStreamMode.Read);
            using var outputStream = File.Create(outputFilePath);
            
            cryptoStream.CopyTo(outputStream);
        }

        /// <summary>
        /// 生成SHA256哈希
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>SHA256哈希值</returns>
        public static string ComputeSHA256Hash(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// 验证SHA256哈希
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="hash">要验证的哈希值</param>
        /// <returns>是否匹配</returns>
        public static bool VerifySHA256Hash(string input, string hash)
        {
            var computedHash = ComputeSHA256Hash(input);
            return computedHash.Equals(hash, StringComparison.OrdinalIgnoreCase);
        }
    }
}
